# 增强版多数据源集成学习使用说明

## 功能概述

增强版多数据源集成学习模块实现了完整的多数据源集成学习功能，支持多种融合方法和概率校准技术，并提供SHAP可解释性分析。

## 主要特性

### 1. 多种融合方法
- **Hard Voting（多数投票）**: 基于预测类别的多数投票
- **Soft Voting（概率加权）**: 基于预测概率的加权平均
- **Logit加权（证据加和）**: 基于logit变换的证据融合
- **Confidence/Abstain投票**: 基于置信度的选择性投票
- **Stacking（元学习）**: 使用元分类器的堆叠集成

### 2. 概率校准
- **Platt Scaling**: 使用逻辑回归的概率校准
- **Isotonic Regression**: 等渗回归校准
- **Temperature Scaling**: 温度缩放校准

### 3. 可解释性分析
- **模态内解释**: 单个模型的特征重要性分析
- **模态间解释**: 不同模型对最终预测的贡献分析
- **SHAP可视化**: 生成详细的SHAP分析图表

## 使用方法

### 1. 通过GUI界面使用

1. **添加数据源**
   - 点击"添加数据源"按钮
   - 选择模型名称（RandomForest、LogisticRegression、SVM等）
   - 选择对应的数据文件
   - 点击"添加"确认

2. **配置融合方法**
   - 选择需要的融合方法（可多选）
   - 选择概率校准方法

3. **开始集成学习**
   - 点击"开始增强集成学习"按钮
   - 等待训练完成

4. **查看结果**
   - 点击"集成结果可视化"查看性能对比
   - 点击"SHAP解释分析"查看可解释性结果

### 2. 通过代码调用

```python
from enhanced_multi_data_ensemble import run_enhanced_multi_data_ensemble_pipeline

# 配置模型数据映射
model_data_mapping = {
    'RandomForest': 'data/dataset_A.csv',
    'LogisticRegression': 'data/dataset_B.csv', 
    'SVM': 'data/dataset_C.csv'
}

# 选择融合方法
fusion_methods = [
    'soft_voting', 'logit_weighted', 'stacking'
]

# 运行集成学习
results = run_enhanced_multi_data_ensemble_pipeline(
    model_data_mapping=model_data_mapping,
    fusion_methods=fusion_methods,
    calibration_method='platt',
    enable_shap=True
)

if results:
    print("集成学习完成！")
    print(f"结果保存在: {results['output_dir']}")
```

## 输出结果

### 1. 性能指标
- 准确率（Accuracy）
- 精确率（Precision）
- 召回率（Recall）
- F1分数（F1-Score）
- AUC值

### 2. 文件输出
- `ensemble_results_[timestamp].json`: 结果摘要
- `detailed_results_[timestamp].joblib`: 详细结果（包含训练好的模型）
- `shap_analysis/`: SHAP分析结果目录
  - `base_models_shap/`: 基础模型SHAP分析
  - `ensemble_shap/`: 集成模型SHAP分析

### 3. SHAP分析图表
- 特征重要性图
- SHAP摘要图
- 模型贡献分析图

## 技术细节

### 1. Logit加权融合
使用logit变换将概率转换为证据空间，然后进行加权融合：
```
logit(p) = log(p / (1-p))
final_logit = Σ(weight_i * logit(p_i)) + bias
final_prob = sigmoid(final_logit)
```

### 2. Confidence/Abstain投票
只使用高置信度的模型进行投票：
- 计算每个模型的预测置信度
- 筛选置信度超过阈值的模型
- 对筛选后的模型进行投票

### 3. 概率校准
对模型输出概率进行校准，提高概率预测的可靠性：
- Platt Scaling: 使用逻辑回归拟合概率
- Isotonic Regression: 使用单调回归校准
- Temperature Scaling: 使用温度参数调整概率分布

## 最佳实践

### 1. 数据准备
- 确保不同数据源的标签一致
- 数据质量要求较高，建议预处理
- 样本数量建议不少于1000个

### 2. 模型选择
- 选择互补性强的模型（如树模型+线性模型）
- 确保基础模型性能合理
- 避免过度拟合的模型

### 3. 融合方法选择
- 数据质量高时推荐使用Logit加权
- 模型性能差异大时推荐使用Stacking
- 需要可解释性时推荐使用Soft Voting

### 4. 概率校准
- 小样本数据推荐使用Platt Scaling
- 大样本数据可以使用Isotonic Regression
- 深度学习模型推荐使用Temperature Scaling

## 注意事项

1. **内存使用**: 大数据集可能消耗较多内存
2. **计算时间**: Stacking方法训练时间较长
3. **SHAP分析**: 大模型的SHAP分析可能较慢
4. **数据一致性**: 确保不同数据源的特征空间兼容

## 故障排除

### 常见问题
1. **导入错误**: 确保所有依赖包已安装
2. **数据格式错误**: 检查CSV文件格式和列名
3. **内存不足**: 减少数据量或关闭SHAP分析
4. **模型训练失败**: 检查数据质量和模型参数

### 依赖包要求
```
scikit-learn>=1.0.0
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
shap>=0.40.0 (可选)
joblib>=1.1.0
```

## 更新日志

### v1.0.0 (2025-09-04)
- 实现基础的多数据源集成学习功能
- 支持5种融合方法
- 集成概率校准功能
- 添加SHAP可解释性分析
- 完整的GUI界面集成
