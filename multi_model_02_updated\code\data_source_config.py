"""
数据源模型配置管理器
专门用于管理多数据源集成学习的配置信息
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DataSourceModelConfig:
    """统一的数据源和模型配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径（支持JSON和YAML格式）
        """
        self.model_data_mapping: Dict[str, str] = {}
        self.preprocessing_configs: Dict[str, Dict] = {}
        self.model_configs: Dict[str, Dict] = {}
        self.ensemble_configs: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        
        # 默认模型配置
        self._default_models = [
            'RandomForest', 'LogisticRegression', 'SVM', 
            'XGBoost', 'LightGBM', 'NaiveBayes'
        ]
        
        # 默认集成方法
        self._default_ensemble_methods = [
            'hard_voting', 'soft_voting', 'logit_weighted',
            'confidence_abstain', 'stacking'
        ]
        
        # 默认校准方法
        self._default_calibration_methods = ['platt', 'isotonic', 'temperature']
        
        if config_file:
            self.load_from_file(config_file)
    
    def validate_data_path(self, path: str) -> bool:
        """
        验证数据路径有效性
        
        Args:
            path: 数据文件路径
            
        Returns:
            bool: 路径是否有效
        """
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                logger.error(f"数据文件不存在: {path}")
                return False
            
            if not path_obj.is_file():
                logger.error(f"路径不是文件: {path}")
                return False
            
            # 检查文件扩展名
            valid_extensions = {'.csv', '.xlsx', '.xls', '.json', '.parquet'}
            if path_obj.suffix.lower() not in valid_extensions:
                logger.warning(f"不支持的文件格式: {path_obj.suffix}")
                return False
            
            # 尝试读取文件头部验证格式
            try:
                if path_obj.suffix.lower() == '.csv':
                    pd.read_csv(path, nrows=1)
                elif path_obj.suffix.lower() in {'.xlsx', '.xls'}:
                    pd.read_excel(path, nrows=1)
                elif path_obj.suffix.lower() == '.json':
                    pd.read_json(path, nrows=1)
                elif path_obj.suffix.lower() == '.parquet':
                    pd.read_parquet(path)
            except Exception as e:
                logger.error(f"文件格式验证失败: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"路径验证失败: {e}")
            return False
    
    def add_mapping(self, model_name: str, data_path: str, 
                   preprocessing_config: Optional[Dict] = None,
                   model_config: Optional[Dict] = None) -> bool:
        """
        添加模型-数据映射
        
        Args:
            model_name: 模型名称
            data_path: 数据路径
            preprocessing_config: 预处理配置
            model_config: 模型配置
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 验证数据路径
            if not self.validate_data_path(data_path):
                return False
            
            # 添加映射
            self.model_data_mapping[model_name] = data_path
            
            # 添加预处理配置
            if preprocessing_config:
                self.preprocessing_configs[model_name] = preprocessing_config
            
            # 添加模型配置
            if model_config:
                self.model_configs[model_name] = model_config
            
            logger.info(f"成功添加映射: {model_name} -> {data_path}")
            return True
            
        except Exception as e:
            logger.error(f"添加映射失败: {e}")
            return False
    
    def remove_mapping(self, model_name: str) -> bool:
        """
        删除模型-数据映射
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if model_name in self.model_data_mapping:
                del self.model_data_mapping[model_name]
                
                # 同时删除相关配置
                if model_name in self.preprocessing_configs:
                    del self.preprocessing_configs[model_name]
                if model_name in self.model_configs:
                    del self.model_configs[model_name]
                
                logger.info(f"成功删除映射: {model_name}")
                return True
            else:
                logger.warning(f"映射不存在: {model_name}")
                return False
                
        except Exception as e:
            logger.error(f"删除映射失败: {e}")
            return False
    
    def get_data_preview(self, model_name: str, n_rows: int = 5) -> Optional[pd.DataFrame]:
        """
        获取数据预览
        
        Args:
            model_name: 模型名称
            n_rows: 预览行数
            
        Returns:
            DataFrame: 数据预览，失败返回None
        """
        try:
            if model_name not in self.model_data_mapping:
                logger.error(f"模型映射不存在: {model_name}")
                return None
            
            data_path = self.model_data_mapping[model_name]
            path_obj = Path(data_path)
            
            if path_obj.suffix.lower() == '.csv':
                return pd.read_csv(data_path, nrows=n_rows)
            elif path_obj.suffix.lower() in {'.xlsx', '.xls'}:
                return pd.read_excel(data_path, nrows=n_rows)
            elif path_obj.suffix.lower() == '.json':
                return pd.read_json(data_path, nrows=n_rows)
            elif path_obj.suffix.lower() == '.parquet':
                df = pd.read_parquet(data_path)
                return df.head(n_rows)
            else:
                logger.error(f"不支持的文件格式: {path_obj.suffix}")
                return None
                
        except Exception as e:
            logger.error(f"获取数据预览失败: {e}")
            return None
    
    def get_optimal_models_for_data(self, data_path: str) -> List[str]:
        """
        根据数据特征推荐最优模型
        
        Args:
            data_path: 数据路径
            
        Returns:
            List[str]: 推荐的模型列表
        """
        try:
            # 读取数据基本信息
            path_obj = Path(data_path)
            if path_obj.suffix.lower() == '.csv':
                df = pd.read_csv(data_path, nrows=1000)  # 采样分析
            else:
                df = pd.read_excel(data_path, nrows=1000)
            
            n_samples, n_features = df.shape
            n_numeric = df.select_dtypes(include=['number']).shape[1]
            n_categorical = n_features - n_numeric
            
            recommended_models = []
            
            # 基于数据特征推荐模型
            if n_samples < 1000:
                # 小数据集
                recommended_models.extend(['LogisticRegression', 'SVM', 'NaiveBayes'])
            elif n_samples > 10000:
                # 大数据集
                recommended_models.extend(['XGBoost', 'LightGBM', 'RandomForest'])
            else:
                # 中等数据集
                recommended_models.extend(['RandomForest', 'XGBoost', 'LogisticRegression'])
            
            # 基于特征类型推荐
            if n_categorical > n_numeric:
                # 分类特征较多
                recommended_models.extend(['RandomForest', 'XGBoost', 'NaiveBayes'])
            else:
                # 数值特征较多
                recommended_models.extend(['SVM', 'LogisticRegression', 'LightGBM'])
            
            # 去重并返回
            return list(set(recommended_models))
            
        except Exception as e:
            logger.error(f"模型推荐失败: {e}")
            return self._default_models.copy()
    
    def save_to_file(self, file_path: str, format: str = 'json') -> bool:
        """
        保存配置到文件
        
        Args:
            file_path: 保存路径
            format: 文件格式 ('json' 或 'yaml')
            
        Returns:
            bool: 是否保存成功
        """
        try:
            config_data = {
                'metadata': {
                    'created_time': datetime.now().isoformat(),
                    'description': '多数据源集成学习配置文件',
                    'version': '1.0'
                },
                'model_data_mapping': self.model_data_mapping,
                'preprocessing_configs': self.preprocessing_configs,
                'model_configs': self.model_configs,
                'ensemble_configs': self.ensemble_configs,
                'supported_models': self._default_models,
                'supported_ensemble_methods': self._default_ensemble_methods,
                'supported_calibration_methods': self._default_calibration_methods
            }
            
            path_obj = Path(file_path)
            path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
            elif format.lower() == 'yaml':
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            logger.info(f"配置已保存到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def load_from_file(self, file_path: str) -> bool:
        """
        从文件加载配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            path_obj = Path(file_path)
            if not path_obj.exists():
                logger.error(f"配置文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                if path_obj.suffix.lower() == '.json':
                    config_data = json.load(f)
                elif path_obj.suffix.lower() in {'.yaml', '.yml'}:
                    config_data = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path_obj.suffix}")
            
            # 加载配置数据
            self.model_data_mapping = config_data.get('model_data_mapping', {})
            self.preprocessing_configs = config_data.get('preprocessing_configs', {})
            self.model_configs = config_data.get('model_configs', {})
            self.ensemble_configs = config_data.get('ensemble_configs', {})
            self.metadata = config_data.get('metadata', {})
            
            logger.info(f"配置已从 {file_path} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要

        Returns:
            Dict: 配置摘要信息
        """
        return {
            'model_count': len(self.model_data_mapping),
            'models': list(self.model_data_mapping.keys()),
            'data_sources': list(self.model_data_mapping.values()),
            'has_preprocessing_configs': len(self.preprocessing_configs) > 0,
            'has_model_configs': len(self.model_configs) > 0,
            'has_ensemble_configs': len(self.ensemble_configs) > 0,
            'metadata': self.metadata
        }

    def validate_all_paths(self) -> Dict[str, bool]:
        """
        验证所有数据路径

        Returns:
            Dict[str, bool]: 每个模型的路径验证结果
        """
        results = {}
        for model_name, data_path in self.model_data_mapping.items():
            results[model_name] = self.validate_data_path(data_path)
        return results

    def get_data_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取数据集详细信息

        Args:
            model_name: 模型名称

        Returns:
            Dict: 数据集信息，失败返回None
        """
        try:
            if model_name not in self.model_data_mapping:
                return None

            data_path = self.model_data_mapping[model_name]
            path_obj = Path(data_path)

            # 读取数据
            if path_obj.suffix.lower() == '.csv':
                df = pd.read_csv(data_path)
            elif path_obj.suffix.lower() in {'.xlsx', '.xls'}:
                df = pd.read_excel(data_path)
            elif path_obj.suffix.lower() == '.json':
                df = pd.read_json(data_path)
            elif path_obj.suffix.lower() == '.parquet':
                df = pd.read_parquet(data_path)
            else:
                return None

            # 分析数据特征
            n_samples, n_features = df.shape
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()

            # 检查缺失值
            missing_values = df.isnull().sum().to_dict()
            missing_percentage = (df.isnull().sum() / len(df) * 100).to_dict()

            return {
                'file_path': data_path,
                'file_size_mb': path_obj.stat().st_size / (1024 * 1024),
                'n_samples': n_samples,
                'n_features': n_features,
                'numeric_features': len(numeric_cols),
                'categorical_features': len(categorical_cols),
                'numeric_columns': numeric_cols,
                'categorical_columns': categorical_cols,
                'missing_values': missing_values,
                'missing_percentage': missing_percentage,
                'data_types': df.dtypes.to_dict()
            }

        except Exception as e:
            logger.error(f"获取数据信息失败: {e}")
            return None

    def create_default_config(self, data_paths: List[str]) -> bool:
        """
        基于数据路径创建默认配置

        Args:
            data_paths: 数据路径列表

        Returns:
            bool: 是否创建成功
        """
        try:
            self.model_data_mapping.clear()

            for i, data_path in enumerate(data_paths):
                if not self.validate_data_path(data_path):
                    continue

                # 推荐最优模型
                recommended_models = self.get_optimal_models_for_data(data_path)

                # 为每个数据源分配一个推荐模型
                if i < len(recommended_models):
                    model_name = recommended_models[i]
                else:
                    model_name = f"Model_{i+1}"

                self.add_mapping(model_name, data_path)

            logger.info(f"创建默认配置完成，包含 {len(self.model_data_mapping)} 个映射")
            return True

        except Exception as e:
            logger.error(f"创建默认配置失败: {e}")
            return False

    def export_for_gui(self) -> Dict[str, Any]:
        """
        导出GUI所需的配置格式

        Returns:
            Dict: GUI配置格式
        """
        return {
            'model_data_mapping': self.model_data_mapping,
            'available_models': self._default_models,
            'available_ensemble_methods': self._default_ensemble_methods,
            'available_calibration_methods': self._default_calibration_methods,
            'config_summary': self.get_config_summary()
        }
