"""
主窗口管理器
整合所有功能模块，提供统一的用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import logging
from typing import Optional

from .core.base_gui import BaseGUI
from .core.event_manager import EventType
from .modules.data_manager import DataManager
from .modules.model_trainer import ModelTrainer

logger = logging.getLogger(__name__)


class MainWindow(BaseGUI):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        # 创建根窗口
        self.root = tk.Tk()
        
        # 设置窗口属性
        self._setup_window()
        
        # 初始化模块
        self.data_manager: Optional[DataManager] = None
        self.model_trainer: Optional[ModelTrainer] = None
        
        super().__init__(self.root)
        
        # 绑定事件
        self._bind_events()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        logger.info("主窗口初始化完成")
    
    def _setup_window(self):
        """设置窗口属性"""
        # 获取配置
        title = self.get_config_value("window.title", "多模型集成机器学习平台")
        geometry = self.get_config_value("window.geometry", "1400x900")
        min_size = self.get_config_value("window.min_size", (1200, 800))
        icon_path = self.get_config_value("window.icon", "icon.ico")
        
        # 设置窗口属性
        self.root.title(title)
        self.root.geometry(geometry)
        self.root.minsize(*min_size)
        
        # 设置图标
        try:
            if Path(icon_path).exists():
                self.root.iconbitmap(icon_path)
        except Exception as e:
            logger.debug(f"设置图标失败: {e}")
    
    def _setup_ui(self):
        """设置主窗口UI"""
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建主要内容区域
        self._create_main_content()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开数据", command=self._open_data_file)
        file_menu.add_command(label="保存项目", command=self._save_project)
        file_menu.add_command(label="加载项目", command=self._load_project)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 数据菜单
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="数据", menu=data_menu)
        data_menu.add_command(label="数据预览", command=self._show_data_preview)
        data_menu.add_command(label="数据验证", command=self._validate_data)
        data_menu.add_command(label="数据预处理", command=self._preprocess_data)
        
        # 模型菜单
        model_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="模型", menu=model_menu)
        model_menu.add_command(label="开始训练", command=self._start_training)
        model_menu.add_command(label="模型比较", command=self._compare_models)
        model_menu.add_command(label="超参数调优", command=self._hyperparameter_tuning)
        
        # 可视化菜单
        viz_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="可视化", menu=viz_menu)
        viz_menu.add_command(label="性能图表", command=self._show_performance_charts)
        viz_menu.add_command(label="特征重要性", command=self._show_feature_importance)
        viz_menu.add_command(label="SHAP分析", command=self._show_shap_analysis)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="设置", command=self._show_settings)
        tools_menu.add_command(label="日志查看器", command=self._show_log_viewer)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="用户指南", command=self._show_user_guide)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = self.factory.create_frame(self.root, relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        # 快捷按钮
        buttons = [
            {"text": "📁 打开", "command": self._open_data_file},
            {"text": "💾 保存", "command": self._save_project},
            {"text": "🚀 训练", "command": self._start_training},
            {"text": "📊 可视化", "command": self._show_performance_charts},
            {"text": "⚙️ 设置", "command": self._show_settings}
        ]
        
        for btn_config in buttons:
            btn = self.factory.create_button(
                toolbar,
                text=btn_config["text"],
                command=btn_config["command"]
            )
            btn.pack(side=tk.LEFT, padx=2)
        
        # 右侧状态显示
        self.factory.create_label(toolbar, text="状态:").pack(side=tk.RIGHT, padx=5)
        status_label = self.factory.create_label(
            toolbar, textvariable=self.status_var, width=20
        )
        status_label.pack(side=tk.RIGHT)
    
    def _create_main_content(self):
        """创建主要内容区域"""
        # 创建主框架
        main_frame = self.factory.create_frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建选项卡
        self.notebook = self.factory.create_notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 数据管理选项卡
        data_tab = self.factory.create_frame(self.notebook)
        self.notebook.add(data_tab, text="📊 数据管理")
        self.data_manager = DataManager(data_tab)
        
        # 模型训练选项卡
        training_tab = self.factory.create_frame(self.notebook)
        self.notebook.add(training_tab, text="🤖 模型训练")
        self.model_trainer = ModelTrainer(training_tab)
        
        # 结果可视化选项卡
        viz_tab = self.factory.create_frame(self.notebook)
        self.notebook.add(viz_tab, text="📈 结果可视化")
        self._create_visualization_tab(viz_tab)
        
        # 集成学习选项卡
        ensemble_tab = self.factory.create_frame(self.notebook)
        self.notebook.add(ensemble_tab, text="🔗 集成学习")
        self._create_ensemble_tab(ensemble_tab)
        
        # 绑定选项卡切换事件
        self.notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)
    
    def _create_visualization_tab(self, parent: tk.Widget):
        """创建可视化选项卡"""
        # 临时占位符
        placeholder = self.factory.create_label(
            parent, text="可视化功能正在开发中...", style="Title.TLabel"
        )
        placeholder.pack(expand=True)
    
    def _create_ensemble_tab(self, parent: tk.Widget):
        """创建集成学习选项卡"""
        # 临时占位符
        placeholder = self.factory.create_label(
            parent, text="集成学习功能正在开发中...", style="Title.TLabel"
        )
        placeholder.pack(expand=True)
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = self.factory.create_frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态文本
        status_label = self.factory.create_label(
            status_frame, textvariable=self.status_var
        )
        status_label.pack(side=tk.LEFT, padx=5)
        
        # 进度条（可选）
        self.progress_var = tk.DoubleVar()
        progress_bar = self.factory.create_progressbar(
            status_frame, variable=self.progress_var, length=200
        )
        progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 时间显示
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        time_label = self.factory.create_label(status_frame, text=current_time)
        time_label.pack(side=tk.RIGHT, padx=5)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据相关事件
        self.subscribe_event(EventType.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventType.DATA_VALIDATED, self._on_data_validated)
        self.subscribe_event(EventType.DATA_PREPROCESSED, self._on_data_preprocessed)
        
        # 订阅模型相关事件
        self.subscribe_event(EventType.MODEL_SELECTED, self._on_model_selected)
        self.subscribe_event(EventType.MODEL_TRAINING_STARTED, self._on_training_started)
        self.subscribe_event(EventType.MODEL_TRAINING_COMPLETED, self._on_training_completed)
        
        # 订阅状态更新事件
        self.subscribe_event(EventType.STATUS_UPDATED, self._on_status_updated)
        self.subscribe_event(EventType.PROGRESS_UPDATED, self._on_progress_updated)
    
    def _on_data_loaded(self, data):
        """数据加载事件处理"""
        logger.info(f"数据已加载: {data['shape']}")
        self.update_status(f"数据已加载: {data['shape'][0]} 行, {data['shape'][1]} 列")
    
    def _on_data_validated(self, data):
        """数据验证事件处理"""
        logger.info("数据验证完成")
        self.update_status("数据验证完成")
    
    def _on_data_preprocessed(self, data):
        """数据预处理事件处理"""
        logger.info("数据预处理完成")
        self.update_status("数据预处理完成")
    
    def _on_model_selected(self, data):
        """模型选择事件处理"""
        count = data['count']
        logger.info(f"已选择 {count} 个模型")
    
    def _on_training_started(self, data):
        """训练开始事件处理"""
        models = data['models']
        logger.info(f"开始训练 {len(models)} 个模型")
        self.update_status(f"正在训练 {len(models)} 个模型...")
    
    def _on_training_completed(self, data):
        """训练完成事件处理"""
        models = data['models']
        success = data['success']
        if success:
            logger.info(f"{len(models)} 个模型训练完成")
            self.update_status("模型训练完成")
        else:
            logger.error("模型训练失败")
            self.update_status("模型训练失败")
    
    def _on_status_updated(self, data):
        """状态更新事件处理"""
        # 这里可以添加额外的状态处理逻辑
        pass
    
    def _on_progress_updated(self, data):
        """进度更新事件处理"""
        progress = data.get('progress', 0)
        self.progress_var.set(progress)
    
    def _on_tab_changed(self, event):
        """选项卡切换事件处理"""
        current_tab = self.notebook.index(self.notebook.select())
        tab_names = ["数据管理", "模型训练", "结果可视化", "集成学习"]
        
        if current_tab < len(tab_names):
            self.update_status(f"当前页面: {tab_names[current_tab]}")
            
            # 发布选项卡切换事件
            self.publish_event(EventType.TAB_CHANGED, {
                'tab_index': current_tab,
                'tab_name': tab_names[current_tab]
            })
    
    # 菜单和工具栏命令方法
    def _open_data_file(self):
        """打开数据文件"""
        if self.data_manager:
            self.data_manager._browse_data_file()
            self.notebook.select(0)  # 切换到数据管理选项卡
    
    def _save_project(self):
        """保存项目"""
        self.show_info("提示", "项目保存功能正在开发中...")
    
    def _load_project(self):
        """加载项目"""
        self.show_info("提示", "项目加载功能正在开发中...")
    
    def _show_data_preview(self):
        """显示数据预览"""
        self.notebook.select(0)  # 切换到数据管理选项卡
    
    def _validate_data(self):
        """验证数据"""
        if self.data_manager:
            self.data_manager._validate_data()
    
    def _preprocess_data(self):
        """预处理数据"""
        if self.data_manager:
            self.data_manager._preprocess_data()
    
    def _start_training(self):
        """开始训练"""
        if self.model_trainer:
            self.model_trainer._start_training()
            self.notebook.select(1)  # 切换到模型训练选项卡
    
    def _compare_models(self):
        """比较模型"""
        self.show_info("提示", "模型比较功能正在开发中...")
    
    def _hyperparameter_tuning(self):
        """超参数调优"""
        self.show_info("提示", "超参数调优功能正在开发中...")
    
    def _show_performance_charts(self):
        """显示性能图表"""
        self.notebook.select(2)  # 切换到可视化选项卡
        self.show_info("提示", "性能图表功能正在开发中...")
    
    def _show_feature_importance(self):
        """显示特征重要性"""
        self.show_info("提示", "特征重要性功能正在开发中...")
    
    def _show_shap_analysis(self):
        """显示SHAP分析"""
        self.show_info("提示", "SHAP分析功能正在开发中...")
    
    def _show_settings(self):
        """显示设置"""
        self.show_info("提示", "设置功能正在开发中...")
    
    def _show_log_viewer(self):
        """显示日志查看器"""
        self.show_info("提示", "日志查看器功能正在开发中...")
    
    def _show_user_guide(self):
        """显示用户指南"""
        self.show_info("提示", "用户指南功能正在开发中...")
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """
多模型集成机器学习平台 v2.0

这是一个功能强大的机器学习平台，支持：
• 多种机器学习算法
• 自动化模型训练和评估
• 集成学习方法
• 可视化分析
• SHAP可解释性分析

开发团队：AI助手
版本：2.0.0
        """
        self.show_info("关于", about_text.strip())
    
    def _on_closing(self):
        """窗口关闭事件处理"""
        if self.ask_yes_no("确认", "确定要退出程序吗？"):
            # 保存配置
            self.save_config()
            
            # 清理资源
            self.clear_event_subscribers()
            
            # 关闭窗口
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        logger.info("启动GUI应用程序")
        self.root.mainloop()


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 创建并运行主窗口
        app = MainWindow()
        app.run()
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        messagebox.showerror("错误", f"应用程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
