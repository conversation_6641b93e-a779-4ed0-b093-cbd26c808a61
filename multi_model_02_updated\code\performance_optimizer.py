"""
性能优化器
提供内存管理、缓存机制和性能监控功能
"""

import gc
import psutil
import time
import functools
import threading
from typing import Dict, Any, Optional, Callable
from pathlib import Path
import logging
import pickle
import hashlib
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_percent: float = 80.0):
        """
        初始化内存管理器
        
        Args:
            max_memory_percent: 最大内存使用百分比
        """
        self.max_memory_percent = max_memory_percent
        self.process = psutil.Process()
        
    def get_memory_usage(self) -> Dict[str, float]:
        """
        获取当前内存使用情况
        
        Returns:
            Dict[str, float]: 内存使用信息
        """
        memory_info = self.process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': self.process.memory_percent(),  # 内存使用百分比
            'available_mb': system_memory.available / 1024 / 1024,  # 可用内存
            'total_mb': system_memory.total / 1024 / 1024  # 总内存
        }
    
    def check_memory_limit(self) -> bool:
        """
        检查是否超过内存限制
        
        Returns:
            bool: 是否超过限制
        """
        memory_percent = self.process.memory_percent()
        return memory_percent > self.max_memory_percent
    
    def force_garbage_collection(self) -> int:
        """
        强制垃圾回收
        
        Returns:
            int: 回收的对象数量
        """
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收对象数量: {collected}")
        return collected
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        before_memory = self.get_memory_usage()
        
        # 强制垃圾回收
        collected = self.force_garbage_collection()
        
        # 等待一小段时间让系统释放内存
        time.sleep(0.1)
        
        after_memory = self.get_memory_usage()
        
        result = {
            'before_mb': before_memory['rss_mb'],
            'after_mb': after_memory['rss_mb'],
            'saved_mb': before_memory['rss_mb'] - after_memory['rss_mb'],
            'collected_objects': collected
        }
        
        logger.info(f"内存优化完成: 释放 {result['saved_mb']:.2f} MB")
        return result


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache", max_cache_size_mb: float = 500.0):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_cache_size_mb: 最大缓存大小（MB）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_cache_size_mb = max_cache_size_mb
        self.cache_index_file = self.cache_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
        
    def _load_cache_index(self) -> Dict[str, Any]:
        """加载缓存索引"""
        try:
            if self.cache_index_file.exists():
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载缓存索引失败: {e}")
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
    
    def _generate_cache_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_cache_size(self) -> float:
        """获取缓存总大小（MB）"""
        total_size = 0
        for cache_file in self.cache_dir.glob("*.pkl"):
            total_size += cache_file.stat().st_size
        return total_size / 1024 / 1024
    
    def cleanup_old_cache(self, max_age_days: int = 7):
        """清理过期缓存"""
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        removed_count = 0
        
        for cache_key, cache_info in list(self.cache_index.items()):
            cache_time = datetime.fromisoformat(cache_info['created_time'])
            if cache_time < cutoff_time:
                cache_file = self.cache_dir / f"{cache_key}.pkl"
                if cache_file.exists():
                    cache_file.unlink()
                    removed_count += 1
                del self.cache_index[cache_key]
        
        if removed_count > 0:
            self._save_cache_index()
            logger.info(f"清理过期缓存: {removed_count} 个文件")
    
    def set_cache(self, key: str, value: Any, metadata: Optional[Dict] = None):
        """设置缓存"""
        try:
            cache_file = self.cache_dir / f"{key}.pkl"
            
            # 保存数据
            with open(cache_file, 'wb') as f:
                pickle.dump(value, f)
            
            # 更新索引
            self.cache_index[key] = {
                'created_time': datetime.now().isoformat(),
                'file_size': cache_file.stat().st_size,
                'metadata': metadata or {}
            }
            
            self._save_cache_index()
            
            # 检查缓存大小限制
            if self.get_cache_size() > self.max_cache_size_mb:
                self.cleanup_old_cache(max_age_days=3)  # 更激进的清理
                
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
    
    def get_cache(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists() and key in self.cache_index:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
        return None
    
    def has_cache(self, key: str) -> bool:
        """检查缓存是否存在"""
        cache_file = self.cache_dir / f"{key}.pkl"
        return cache_file.exists() and key in self.cache_index


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.metrics = {}
        self.lock = threading.Lock()
    
    def record_execution_time(self, func_name: str, execution_time: float):
        """记录执行时间"""
        with self.lock:
            if func_name not in self.metrics:
                self.metrics[func_name] = {
                    'total_time': 0.0,
                    'call_count': 0,
                    'avg_time': 0.0,
                    'max_time': 0.0,
                    'min_time': float('inf')
                }
            
            metrics = self.metrics[func_name]
            metrics['total_time'] += execution_time
            metrics['call_count'] += 1
            metrics['avg_time'] = metrics['total_time'] / metrics['call_count']
            metrics['max_time'] = max(metrics['max_time'], execution_time)
            metrics['min_time'] = min(metrics['min_time'], execution_time)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self.lock:
            return {
                'functions': dict(self.metrics),
                'total_functions': len(self.metrics),
                'report_time': datetime.now().isoformat()
            }
    
    def reset_metrics(self):
        """重置性能指标"""
        with self.lock:
            self.metrics.clear()


# 全局实例
memory_manager = MemoryManager()
cache_manager = CacheManager()
performance_monitor = PerformanceMonitor()


def memory_optimized(func: Callable) -> Callable:
    """内存优化装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 检查内存使用
        if memory_manager.check_memory_limit():
            logger.warning("内存使用过高，执行垃圾回收")
            memory_manager.optimize_memory()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # 函数执行后检查内存
            if memory_manager.check_memory_limit():
                memory_manager.force_garbage_collection()
    
    return wrapper


def cached(cache_key_func: Optional[Callable] = None, expire_hours: int = 24):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_cache_key(func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = cache_manager.get_cache(cache_key)
            if cached_result is not None:
                logger.debug(f"从缓存获取结果: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set_cache(
                cache_key, 
                result, 
                metadata={
                    'function': func.__name__,
                    'expire_hours': expire_hours
                }
            )
            
            return result
        
        return wrapper
    return decorator


def performance_monitored(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            performance_monitor.record_execution_time(func.__name__, execution_time)
            
            if execution_time > 1.0:  # 记录耗时超过1秒的函数
                logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.2f}s")
    
    return wrapper


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    return {
        'memory': memory_manager.get_memory_usage(),
        'cache_size_mb': cache_manager.get_cache_size(),
        'performance': performance_monitor.get_performance_report(),
        'timestamp': datetime.now().isoformat()
    }
