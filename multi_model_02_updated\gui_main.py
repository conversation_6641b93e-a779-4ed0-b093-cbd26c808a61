#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模型集成机器学习平台 - GUI主界面
提供完整的图形用户界面，实现所有命令行功能的可视化操作
"""

import sys
from pathlib import Path
import tkinter as tk

# 修复matplotlib后端问题，防止Tkinter冲突
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import json
from datetime import datetime


class TimerManager:
    """Manages GUI timers to prevent conflicts"""
    def __init__(self, root=None):
        self.root = root
        self.timers = {}
    
    def set_root(self, root):
        """Set the root window for timer operations"""
        self.root = root
    
    def set_timer(self, name, delay, callback):
        """Set a timer, canceling any existing timer with the same name"""
        if self.root is None:
            return None
            
        if name in self.timers:
            try:
                self.root.after_cancel(self.timers[name])
            except:
                pass
        
        timer_id = self.root.after(delay, callback)
        self.timers[name] = timer_id
        return timer_id
    
    def cancel_timer(self, name):
        """Cancel a specific timer"""
        if name in self.timers:
            try:
                self.root.after_cancel(self.timers[name])
            except:
                pass
            del self.timers[name]
    
    def cancel_all(self):
        """Cancel all timers"""
        for timer_id in self.timers.values():
            try:
                self.root.after_cancel(timer_id)
            except:
                pass
        self.timers.clear()


# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

# 导入项目模块
try:
    from config import MODEL_NAMES, MODEL_DISPLAY_NAMES, OUTPUT_PATH, CACHE_PATH
    from logger import get_logger
    from data_preprocessing import DataPreprocessor, load_and_preprocess_data
    from model_training import MODEL_TRAINERS
    from plot_utils import PlotManager
    from model_ensemble import run_ensemble_pipeline
    # from plot_ensemble import visualize_ensemble_results  # 使用安全的可视化模块替代
    from multi_data_ensemble import run_multi_data_ensemble_pipeline
    from external_validation import run_external_validation
    from session_gui import SessionManagerGUI
    from session_utils import create_new_session, get_active_session_id
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的环境中运行GUI程序")
    sys.exit(1)
# 复现性：入口尽早设置全局随机种子（与CLI一致）
try:
    from config import set_global_seed, RANDOM_SEED
    set_global_seed(RANDOM_SEED)
except Exception:
    pass


class MLPlatformGUI:
    """
    多模型集成机器学习平台GUI主类
    """

    def __init__(self):
        """初始化GUI界面"""
        init_start = datetime.now()  # 记录总初始化开始时间
        self.logger = get_logger("GUI")
        self.logger.info("开始初始化GUI界面...")

        # 初始化窗口
        self._init_window()

        # 初始化类属性
        self._init_attributes()

        # 设置图标
        self._set_window_icon()

        # 初始化变量
        self.current_data_path = tk.StringVar()
        self.selected_models = []
        self.training_progress = tk.DoubleVar()
        self.status_text = tk.StringVar(value="就绪")

        # 配置更新相关
        self.config_update_pending = False
        self.config_update_timer = None

        # 绑定数据路径变化事件
        self.current_data_path.trace_add('write', lambda *_: self.schedule_config_update())

        # 初始化组件
        self.logger.debug("初始化数据预处理器和绘图管理器...")
        self.data_preprocessor = DataPreprocessor()
        self.plot_manager = PlotManager()

        # 初始化字体管理器，强制使用英文避免字体问题
        try:
            from font_manager import initialize_fonts
            initialize_fonts()
            self.logger.info("字体管理器初始化成功，使用英文字体")
        except Exception as e:
            self.logger.warning(f"字体管理器初始化失败: {e}")

        # 初始化会话管理器
        try:
            session_start = datetime.now()
            self.session_manager_gui = SessionManagerGUI(self.root)

            # 初始化会话GUI集成
            from session_gui_integration import create_session_gui_integration
            self.session_integration = create_session_gui_integration(self)
            self.log_performance("会话管理器初始化", session_start)
        except Exception as e:
            self.logger.warning(f"会话管理器初始化失败: {e}")
            self.session_manager_gui = None
            self.session_integration = None

        # 线程通信队列
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()

        # 初始化定时器管理
        self.timer_ids = []
        self.is_running = True
        self.check_results_timer_id = None  # 专门用于 check_results 的定时器ID
        self.update_time_timer_id = None    # 专门用于 update_time 的定时器ID
        
        # 初始化定时器管理器
        self.timer_manager = TimerManager()
        self.timer_manager.set_root(self.root)
        
        # 初始化模型变量（一次性创建，避免重复）
        self.model_vars = {}
        for model_name in MODEL_NAMES:
            self.model_vars[model_name] = tk.BooleanVar()

        # 创建界面
        gui_start = datetime.now()
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()
        self.log_performance("GUI组件创建", gui_start)

        # 初始化GUI功能模块
        func_start = datetime.now()
        from gui_functions import GUIFunctions
        self.functions = GUIFunctions(self)
        self.log_performance("GUI功能模块初始化", func_start)

        # 启动定时器检查任务结果
        self.check_results()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化配置显示
        self.root.after(100, self.refresh_config_display)

        # 记录总初始化时间
        self.log_performance("GUI界面初始化", init_start)
        self.logger.info("GUI界面初始化完成")

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载数据...", command=self.load_data, accelerator="Ctrl+O")
        file_menu.add_command(label="保存项目...", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="加载项目...", command=self.load_project, accelerator="Ctrl+L")
        file_menu.add_separator()
        file_menu.add_command(label="导出结果...", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.quit_app, accelerator="Ctrl+Q")

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清除缓存", command=self.clear_cache)
        edit_menu.add_command(label="重置设置", command=self.reset_settings)

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="显示日志", command=self.toggle_log_panel)
        view_menu.add_command(label="全屏", command=self.toggle_fullscreen)

        # 会话菜单
        session_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="会话", menu=session_menu)
        session_menu.add_command(label="会话管理器", command=self.show_session_manager)
        session_menu.add_command(label="新建会话", command=self.create_new_session)
        session_menu.add_separator()
        session_menu.add_command(label="自动创建会话", command=self.auto_create_session)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据质量检查", command=self.data_quality_check)
        tools_menu.add_command(label="模型选择建议", command=self.model_selection_advisor)
        tools_menu.add_command(label="配置生成器", command=self.config_generator)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

        # 绑定快捷键
        self.root.bind('<Control-o>', lambda _: self.load_data())
        self.root.bind('<Control-s>', lambda _: self.save_project())
        self.root.bind('<Control-l>', lambda _: self.load_project())
        self.root.bind('<Control-q>', lambda _: self.quit_app())

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

        # 数据操作按钮
        ttk.Button(toolbar, text="📁 加载数据", command=self.load_data).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # 模型操作按钮
        ttk.Button(toolbar, text="🚀 训练模型", command=self.train_models).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📊 可视化", command=self.visualize_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📈 比较模型", command=self.compare_models).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # 高级功能按钮
        ttk.Button(toolbar, text="🎯 最佳模型", command=self.select_best_model).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🚀 完整分析", command=self.run_complete_analysis).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🔧 超参数调优", command=self.hyperparameter_tuning).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🔬 DeLong检验", command=lambda: self.functions.view_delong_results()).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📋 生成报告", command=self.generate_report).pack(side=tk.LEFT, padx=2)

        # 右侧状态显示（移除当前数据显示，已移至配置面板）
        ttk.Label(toolbar, text="状态:").pack(side=tk.RIGHT, padx=5)
        ttk.Label(toolbar, textvariable=self.status_text, width=20).pack(side=tk.RIGHT)

    def create_main_layout(self):
        """创建主要布局"""
        self.logger.debug("开始创建主要布局...")
        
        # 创建主要的PanedWindow
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.logger.debug("主PanedWindow创建完成")

        # 左侧导航面板
        self.logger.debug("创建左侧导航面板...")
        self.create_navigation_panel(main_paned)

        # 中央工作区域
        self.logger.debug("创建中央工作区域...")
        self.create_work_area(main_paned)

        # 右侧配置面板
        self.logger.debug("创建右侧配置面板...")
        self.create_config_panel(main_paned)
        
        self.logger.debug("主要布局创建完成")

    def create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        nav_frame = ttk.LabelFrame(parent, text="功能导航", width=250)
        parent.add(nav_frame, weight=1)

        # 创建树形导航
        self.nav_tree = ttk.Treeview(nav_frame, show="tree")
        self.nav_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加导航项目
        data_node = self.nav_tree.insert("", "end", text="📁 数据管理", open=True)
        self.nav_tree.insert(data_node, "end", text="加载数据", tags=("load_data",))
        self.nav_tree.insert(data_node, "end", text="数据预览", tags=("preview_data",))
        self.nav_tree.insert(data_node, "end", text="数据验证", tags=("validate_data",))
        self.nav_tree.insert(data_node, "end", text="数据预处理", tags=("preprocess_data",))
        self.nav_tree.insert(data_node, "end", text="数据探索", tags=("data_exploration",))

        model_node = self.nav_tree.insert("", "end", text="🤖 模型训练", open=True)
        self.nav_tree.insert(model_node, "end", text="模型选择", tags=("select_models",))
        self.nav_tree.insert(model_node, "end", text="参数配置", tags=("config_params",))
        self.nav_tree.insert(model_node, "end", text="开始训练", tags=("start_training",))
        self.nav_tree.insert(model_node, "end", text="超参数调优", tags=("hyperparameter_tuning",))

        viz_node = self.nav_tree.insert("", "end", text="📊 结果可视化", open=True)
        self.nav_tree.insert(viz_node, "end", text="单模型可视化", tags=("single_viz",))
        self.nav_tree.insert(viz_node, "end", text="模型比较", tags=("compare_viz",))
        self.nav_tree.insert(viz_node, "end", text="性能报告", tags=("performance_report",))

        ensemble_node = self.nav_tree.insert("", "end", text="🤝 集成学习", open=True)
        self.nav_tree.insert(ensemble_node, "end", text="单数据源集成", tags=("single_ensemble",))
        self.nav_tree.insert(ensemble_node, "end", text="多数据源集成", tags=("multi_ensemble",))
        self.nav_tree.insert(ensemble_node, "end", text="外部验证", tags=("external_validation",))

        # 绑定点击事件
        self.nav_tree.bind("<<TreeviewSelect>>", self.on_nav_select)

    def create_work_area(self, parent):
        """创建中央工作区域"""
        work_frame = ttk.LabelFrame(parent, text="工作区域")
        parent.add(work_frame, weight=3)

        # 创建选项卡
        self.notebook = ttk.Notebook(work_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 数据管理选项卡
        self.create_data_tab()

        # 模型训练选项卡
        self.create_training_tab()

        # 结果可视化选项卡
        self.create_visualization_tab()

        # 集成学习选项卡
        self.create_ensemble_tab()

        # 初始化并创建数据探索选项卡
        from gui_data_exploration import DataExplorationGUI
        self.data_exploration = DataExplorationGUI(self)
        self.data_exploration.create_exploration_tab(self.notebook)

    def create_config_panel(self, parent):
        """创建右侧配置面板"""
        config_frame = ttk.LabelFrame(parent, text="配置与日志", width=350)
        parent.add(config_frame, weight=1)

        # 创建配置和日志的选项卡
        config_notebook = ttk.Notebook(config_frame)
        config_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 配置选项卡
        config_tab = ttk.Frame(config_notebook)
        config_notebook.add(config_tab, text="配置")

        # 创建配置内容
        self.create_config_content(config_tab)

        # 日志选项卡
        log_tab = ttk.Frame(config_notebook)
        config_notebook.add(log_tab, text="日志")

        # 创建日志显示区域
        self.log_text = tk.Text(log_tab, wrap=tk.WORD, height=15, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_config_content(self, parent):
        """创建配置选项卡内容"""
        # 创建滚动区域
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        def update_scrollregion(event=None):
            """Update canvas scrollregion when frame size changes"""
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        scrollable_frame.bind("<Configure>", update_scrollregion)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 当前数据信息区域
        data_info_frame = ttk.LabelFrame(scrollable_frame, text="当前数据信息")
        data_info_frame.pack(fill=tk.X, padx=8, pady=8)

        # 数据路径显示
        ttk.Label(data_info_frame, text="数据文件:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, padx=8, pady=3)
        self.config_data_path_label = ttk.Label(data_info_frame, textvariable=self.current_data_path,
                                               font=('Arial', 9), foreground='blue', wraplength=320)
        self.config_data_path_label.pack(anchor=tk.W, padx=20, pady=2)

        # 数据状态显示
        ttk.Label(data_info_frame, text="数据状态:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, padx=8, pady=3)
        self.data_status_var = tk.StringVar(value="未加载数据")
        ttk.Label(data_info_frame, textvariable=self.data_status_var,
                 font=('Arial', 9), foreground='gray').pack(anchor=tk.W, padx=20, pady=2)

        # 模型配置区域
        model_config_frame = ttk.LabelFrame(scrollable_frame, text="模型配置")
        model_config_frame.pack(fill=tk.X, padx=8, pady=8)

        # 选中的模型显示
        ttk.Label(model_config_frame, text="选中模型:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, padx=8, pady=3)
        self.selected_models_var = tk.StringVar(value="未选择模型")
        selected_models_label = ttk.Label(model_config_frame, textvariable=self.selected_models_var,
                                         font=('Arial', 9), wraplength=320)
        selected_models_label.pack(anchor=tk.W, padx=20, pady=2)

        # 训练参数显示
        ttk.Label(model_config_frame, text="训练参数:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, padx=8, pady=3)

        params_frame = ttk.Frame(model_config_frame)
        params_frame.pack(fill=tk.X, padx=20, pady=3)

        # 测试集比例
        test_size_frame = ttk.Frame(params_frame)
        test_size_frame.pack(fill=tk.X, pady=2)
        ttk.Label(test_size_frame, text="测试集比例:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_test_size_var = tk.StringVar(value="0.20")
        ttk.Label(test_size_frame, textvariable=self.config_test_size_var,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side=tk.RIGHT)

        # 随机种子
        seed_frame = ttk.Frame(params_frame)
        seed_frame.pack(fill=tk.X, pady=2)
        ttk.Label(seed_frame, text="随机种子:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_seed_var = tk.StringVar(value="42")
        ttk.Label(seed_frame, textvariable=self.config_seed_var,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side=tk.RIGHT)

        # 特征缩放
        scaling_frame = ttk.Frame(params_frame)
        scaling_frame.pack(fill=tk.X, pady=2)
        ttk.Label(scaling_frame, text="特征缩放:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_scaling_var = tk.StringVar(value="standard")
        ttk.Label(scaling_frame, textvariable=self.config_scaling_var,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side=tk.RIGHT)

        # 超参数调优配置区域
        tuning_config_frame = ttk.LabelFrame(scrollable_frame, text="超参数调优配置")
        tuning_config_frame.pack(fill=tk.X, padx=8, pady=8)

        # 调优状态
        tuning_status_frame = ttk.Frame(tuning_config_frame)
        tuning_status_frame.pack(fill=tk.X, padx=8, pady=3)
        ttk.Label(tuning_status_frame, text="调优状态:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_tuning_status_var = tk.StringVar(value="未启用")
        ttk.Label(tuning_status_frame, textvariable=self.config_tuning_status_var,
                 font=('Arial', 9, 'bold'), foreground='gray').pack(side=tk.RIGHT)

        # 试验次数
        trials_frame = ttk.Frame(tuning_config_frame)
        trials_frame.pack(fill=tk.X, padx=8, pady=2)
        ttk.Label(trials_frame, text="试验次数:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_trials_var = tk.StringVar(value="50")
        ttk.Label(trials_frame, textvariable=self.config_trials_var,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side=tk.RIGHT)

        # 调优策略
        strategy_frame = ttk.Frame(tuning_config_frame)
        strategy_frame.pack(fill=tk.X, padx=8, pady=2)
        ttk.Label(strategy_frame, text="调优策略:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_strategy_var = tk.StringVar(value="TPE")
        ttk.Label(strategy_frame, textvariable=self.config_strategy_var,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side=tk.RIGHT)

        # 系统状态区域
        system_frame = ttk.LabelFrame(scrollable_frame, text="系统状态")
        system_frame.pack(fill=tk.X, padx=8, pady=8)

        # 训练状态
        training_status_frame = ttk.Frame(system_frame)
        training_status_frame.pack(fill=tk.X, padx=8, pady=3)
        ttk.Label(training_status_frame, text="训练状态:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_training_status_var = tk.StringVar(value="就绪")
        ttk.Label(training_status_frame, textvariable=self.config_training_status_var,
                 font=('Arial', 9, 'bold'), foreground='green').pack(side=tk.RIGHT)

        # 内存使用
        memory_frame = ttk.Frame(system_frame)
        memory_frame.pack(fill=tk.X, padx=8, pady=2)
        ttk.Label(memory_frame, text="内存使用:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.config_memory_var = tk.StringVar(value="--")
        ttk.Label(memory_frame, textvariable=self.config_memory_var,
                 font=('Arial', 9, 'bold'), foreground='orange').pack(side=tk.RIGHT)

        # 快捷操作区域
        actions_frame = ttk.LabelFrame(scrollable_frame, text="快捷操作")
        actions_frame.pack(fill=tk.X, padx=8, pady=8)

        # 操作按钮
        ttk.Button(actions_frame, text="🔄 刷新配置",
                  command=self.refresh_config_display).pack(fill=tk.X, padx=8, pady=3)
        ttk.Button(actions_frame, text="💾 保存配置",
                  command=self.save_current_config).pack(fill=tk.X, padx=8, pady=3)
        ttk.Button(actions_frame, text="📁 加载配置",
                  command=self.load_config_file).pack(fill=tk.X, padx=8, pady=3)

        # 配置滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # 状态文本
        ttk.Label(status_frame, textvariable=self.status_text).pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.training_progress,
                                          maximum=100, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)

        # 时间显示
        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        if not self.is_running:
            return
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.config(text=current_time)

            # 取消之前的定时器（如果存在）
            if self.update_time_timer_id is not None:
                try:
                    self.root.after_cancel(self.update_time_timer_id)
                except tk.TclError:
                    pass

            # 设置新的定时器
            self.update_time_timer_id = self.root.after(1000, self.update_time)
        except tk.TclError:
            # GUI已关闭，停止定时器
            self.is_running = False
            self.update_time_timer_id = None

    def create_data_tab(self):
        """创建数据管理选项卡"""
        data_tab = ttk.Frame(self.notebook)
        self.notebook.add(data_tab, text="📁 数据管理")

        # 数据加载区域
        load_frame = ttk.LabelFrame(data_tab, text="数据加载")
        load_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(load_frame, text="数据文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.data_path_entry = ttk.Entry(load_frame, textvariable=self.current_data_path, width=50)
        self.data_path_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(load_frame, text="浏览...", command=self.browse_data_file).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(load_frame, text="加载", command=self.load_data_file).grid(row=0, column=3, padx=5, pady=5)

        # 数据预览区域
        preview_frame = ttk.LabelFrame(data_tab, text="数据预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建表格显示数据
        self.data_tree = ttk.Treeview(preview_frame, show="headings")
        data_scrollbar_y = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        data_scrollbar_x = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=data_scrollbar_y.set, xscrollcommand=data_scrollbar_x.set)

        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        data_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 数据信息显示
        info_frame = ttk.LabelFrame(data_tab, text="数据信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        self.data_info_text = tk.Text(info_frame, height=6, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.data_info_text.yview)
        self.data_info_text.configure(yscrollcommand=info_scrollbar.set)

        self.data_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_training_tab(self):
        """创建模型训练选项卡"""
        training_tab = ttk.Frame(self.notebook)
        self.notebook.add(training_tab, text="🤖 模型训练")

        # 模型选择区域
        model_frame = ttk.LabelFrame(training_tab, text="模型选择")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建模型复选框
        row, col = 0, 0
        for model_name in MODEL_NAMES:
            display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
            
            # 绑定变化事件到配置更新
            self.model_vars[model_name].trace_add('write', lambda *_: self.schedule_config_update())

            cb = ttk.Checkbutton(model_frame, text=display_name, variable=self.model_vars[model_name])
            cb.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)

            col += 1
            if col >= 3:  # 每行3个
                col = 0
                row += 1

        # 全选/取消全选按钮
        button_frame = ttk.Frame(model_frame)
        button_frame.grid(row=row+1, column=0, columnspan=3, pady=5)
        ttk.Button(button_frame, text="全选", command=self.select_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消全选", command=self.deselect_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="推荐选择", command=self.recommend_models).pack(side=tk.LEFT, padx=5)

        # 训练参数配置区域
        param_frame = ttk.LabelFrame(training_tab, text="训练参数")
        param_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(param_frame, text="测试集比例:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.test_size_var = tk.DoubleVar(value=0.2)
        # 绑定变化事件
        self.test_size_var.trace_add('write', lambda *_: self.schedule_config_update())
        self.test_size_var.trace_add('write', self._sync_test_size_to_training_config)
        test_size_scale = ttk.Scale(param_frame, from_=0.1, to=0.5, variable=self.test_size_var, orient=tk.HORIZONTAL)
        test_size_scale.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        self.test_size_label = ttk.Label(param_frame, text="0.2")
        self.test_size_label.grid(row=0, column=2, padx=5, pady=5)
        test_size_scale.configure(command=self.update_test_size_label)

        ttk.Label(param_frame, text="随机种子:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.random_seed_var = tk.IntVar(value=42)
        # 绑定变化事件
        self.random_seed_var.trace_add('write', lambda *_: self.schedule_config_update())
        ttk.Entry(param_frame, textvariable=self.random_seed_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(param_frame, text="特征缩放:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.scaling_var = tk.StringVar(value="standard")
        # 绑定变化事件
        self.scaling_var.trace_add('write', lambda *_: self.schedule_config_update())
        scaling_combo = ttk.Combobox(param_frame, textvariable=self.scaling_var,
                                   values=["standard", "minmax", "robust", "none"], state="readonly")
        scaling_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # 超参数调优配置区域
        tuning_frame = ttk.LabelFrame(training_tab, text="超参数调优配置")
        tuning_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行：调优开关和试验次数
        row1_frame = ttk.Frame(tuning_frame)
        row1_frame.pack(fill=tk.X, padx=5, pady=5)

        self.enable_tuning_var = tk.BooleanVar(value=False)
        # 绑定变化事件
        self.enable_tuning_var.trace_add('write', lambda *_: self.schedule_config_update())
        ttk.Checkbutton(row1_frame, text="启用超参数调优", variable=self.enable_tuning_var,
                       command=self.toggle_tuning_options).pack(side=tk.LEFT, padx=5)

        ttk.Label(row1_frame, text="试验次数:").pack(side=tk.LEFT, padx=(20, 5))
        self.n_trials_var = tk.IntVar(value=50)
        # 绑定变化事件
        self.n_trials_var.trace_add('write', lambda *_: self.schedule_config_update())
        trials_spinbox = ttk.Spinbox(row1_frame, from_=10, to=200, textvariable=self.n_trials_var, width=10)
        trials_spinbox.pack(side=tk.LEFT, padx=5)

        # 第二行：调优策略和超时设置
        row2_frame = ttk.Frame(tuning_frame)
        row2_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(row2_frame, text="调优策略:").pack(side=tk.LEFT, padx=5)
        self.tuning_strategy_var = tk.StringVar(value="TPE")
        # 绑定变化事件
        self.tuning_strategy_var.trace_add('write', lambda *_: self.schedule_config_update())
        strategy_combo = ttk.Combobox(row2_frame, textvariable=self.tuning_strategy_var,
                                    values=["TPE", "Random", "CmaEs"], state="readonly", width=10)
        strategy_combo.pack(side=tk.LEFT, padx=5)

        ttk.Label(row2_frame, text="超时(分钟):").pack(side=tk.LEFT, padx=(20, 5))
        self.tuning_timeout_var = tk.IntVar(value=30)
        timeout_spinbox = ttk.Spinbox(row2_frame, from_=5, to=120, textvariable=self.tuning_timeout_var, width=10)
        timeout_spinbox.pack(side=tk.LEFT, padx=5)

        # 第三行：并行设置和评估指标
        row3_frame = ttk.Frame(tuning_frame)
        row3_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(row3_frame, text="并行作业数:").pack(side=tk.LEFT, padx=5)
        self.tuning_n_jobs_var = tk.IntVar(value=1)
        jobs_spinbox = ttk.Spinbox(row3_frame, from_=1, to=8, textvariable=self.tuning_n_jobs_var, width=10)
        jobs_spinbox.pack(side=tk.LEFT, padx=5)

        ttk.Label(row3_frame, text="评估指标:").pack(side=tk.LEFT, padx=(20, 5))
        self.tuning_metric_var = tk.StringVar(value="roc_auc")
        metric_combo = ttk.Combobox(row3_frame, textvariable=self.tuning_metric_var,
                                  values=["roc_auc", "accuracy", "f1", "precision", "recall"],
                                  state="readonly", width=12)
        metric_combo.pack(side=tk.LEFT, padx=5)

        # 第四行：早停机制配置
        row4_frame = ttk.Frame(tuning_frame)
        row4_frame.pack(fill=tk.X, padx=5, pady=5)

        self.enable_early_stopping_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row4_frame, text="启用早停机制", variable=self.enable_early_stopping_var,
                       command=self.toggle_early_stopping_options).pack(side=tk.LEFT, padx=5)

        ttk.Label(row4_frame, text="耐心值:").pack(side=tk.LEFT, padx=(20, 5))
        self.early_stopping_patience_var = tk.IntVar(value=10)
        patience_spinbox = ttk.Spinbox(row4_frame, from_=5, to=50, textvariable=self.early_stopping_patience_var, width=8)
        patience_spinbox.pack(side=tk.LEFT, padx=5)

        ttk.Label(row4_frame, text="最小改善:").pack(side=tk.LEFT, padx=(20, 5))
        self.min_improvement_var = tk.DoubleVar(value=0.001)
        improvement_spinbox = ttk.Spinbox(row4_frame, from_=0.0001, to=0.01, increment=0.0001,
                                        textvariable=self.min_improvement_var, width=10, format="%.4f")
        improvement_spinbox.pack(side=tk.LEFT, padx=5)

        # 严格复现模式配置区域
        reproducibility_frame = ttk.LabelFrame(training_tab, text="严格复现模式配置")
        reproducibility_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行：严格复现模式开关
        repro_row1_frame = ttk.Frame(reproducibility_frame)
        repro_row1_frame.pack(fill=tk.X, padx=5, pady=5)

        self.strict_reproducibility_var = tk.BooleanVar(value=False)
        # 绑定变化事件
        self.strict_reproducibility_var.trace_add('write', lambda *_: self.schedule_config_update())
        ttk.Checkbutton(repro_row1_frame, text="启用严格复现模式",
                       variable=self.strict_reproducibility_var,
                       command=self.toggle_strict_reproducibility).pack(side=tk.LEFT, padx=5)

        # 添加说明标签
        ttk.Label(repro_row1_frame, text="(限制线程数，使用确定性设定)",
                 foreground="gray").pack(side=tk.LEFT, padx=(10, 5))

        # 第二行：线程数和CPU强制使用设置
        repro_row2_frame = ttk.Frame(reproducibility_frame)
        repro_row2_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(repro_row2_frame, text="最大线程数:").pack(side=tk.LEFT, padx=5)
        self.repro_num_threads_var = tk.IntVar(value=1)
        # 绑定变化事件
        self.repro_num_threads_var.trace_add('write', lambda *_: self.schedule_config_update())
        self.threads_spinbox = ttk.Spinbox(repro_row2_frame, from_=1, to=8,
                                          textvariable=self.repro_num_threads_var, width=8)
        self.threads_spinbox.pack(side=tk.LEFT, padx=5)

        self.repro_enforce_cpu_var = tk.BooleanVar(value=False)
        # 绑定变化事件
        self.repro_enforce_cpu_var.trace_add('write', lambda *_: self.schedule_config_update())
        self.enforce_cpu_checkbox = ttk.Checkbutton(repro_row2_frame, text="强制使用CPU",
                                                   variable=self.repro_enforce_cpu_var)
        self.enforce_cpu_checkbox.pack(side=tk.LEFT, padx=(20, 5))

        # 初始状态下禁用详细设置
        self.repro_detail_widgets = [self.threads_spinbox, self.enforce_cpu_checkbox]
        for widget in self.repro_detail_widgets:
            widget.config(state=tk.DISABLED)

        # 训练控制区域
        control_frame = ttk.LabelFrame(training_tab, text="训练控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="🚀 开始训练", command=self.start_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="⏹ 停止训练", command=self.stop_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="🔧 超参数调优", command=self.start_hyperparameter_tuning).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="📊 调优结果", command=self.show_tuning_results).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="🔬 DeLong检验", command=lambda: self.functions.view_delong_results()).pack(side=tk.LEFT, padx=5, pady=5)

        # 训练进度显示
        progress_frame = ttk.LabelFrame(training_tab, text="训练进度")
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.training_log = tk.Text(progress_frame, wrap=tk.WORD)
        training_scrollbar = ttk.Scrollbar(progress_frame, orient=tk.VERTICAL, command=self.training_log.yview)
        self.training_log.configure(yscrollcommand=training_scrollbar.set)

        self.training_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        training_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_visualization_tab(self):
        """创建结果可视化选项卡（优化版，支持滚动和缩放）"""
        viz_tab = ttk.Frame(self.notebook)
        self.notebook.add(viz_tab, text="📊 结果可视化")

        # 可视化控制区域
        control_frame = ttk.LabelFrame(viz_tab, text="可视化控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行控件
        row1_frame = ttk.Frame(control_frame)
        row1_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(row1_frame, text="选择模型:").pack(side=tk.LEFT, padx=(0, 5))
        self.viz_model_var = tk.StringVar()
        self.viz_model_combo = ttk.Combobox(row1_frame, textvariable=self.viz_model_var,
                                          values=MODEL_NAMES, state="readonly", width=15)
        self.viz_model_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(row1_frame, text="图表类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.chart_type_var = tk.StringVar(value="ROC曲线")
        chart_type_combo = ttk.Combobox(row1_frame, textvariable=self.chart_type_var,
                                      values=["ROC曲线", "混淆矩阵", "特征重要性", "学习曲线", "性能比较", "SHAP分析"],
                                      state="readonly", width=12)
        chart_type_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 第二行按钮
        row2_frame = ttk.Frame(control_frame)
        row2_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        ttk.Button(row2_frame, text="📈 单模型可视化",
                  command=self.single_model_visualization).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2_frame, text="📊 模型比较",
                  command=self.model_comparison).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2_frame, text="🔬 DeLong检验",
                  command=lambda: self.functions.view_delong_results()).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2_frame, text="📋 生成报告",
                  command=self.generate_performance_report).pack(side=tk.LEFT, padx=(0, 5))

        # 图表操作按钮
        ttk.Separator(row2_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(row2_frame, text="🔄 刷新图表",
                  command=self.refresh_chart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2_frame, text="💾 保存图表",
                  command=self.save_chart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2_frame, text="📄 详细报告",
                  command=self.generate_detailed_report).pack(side=tk.LEFT, padx=(0, 5))

        # 图表显示区域（优化版）
        chart_frame = ttk.LabelFrame(viz_tab, text="图表显示 (支持滚动和缩放)")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建图表状态显示
        status_frame = ttk.Frame(chart_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=2)

        self.chart_status_var = tk.StringVar(value="请选择模型和图表类型，然后点击可视化按钮")
        ttk.Label(status_frame, textvariable=self.chart_status_var,
                 font=("Arial", 9), foreground="gray").pack(side=tk.LEFT)

        # 图表操作提示
        ttk.Label(status_frame, text="💡 提示: 使用鼠标滚轮缩放，Ctrl+滚轮水平滚动",
                 font=("Arial", 8), foreground="blue").pack(side=tk.RIGHT)

        # 初始化图表相关属性
        self.chart_canvas = None  # 将在需要时创建
        self.chart_scrollbars = []  # 滚动条列表

        # 创建初始提示区域
        self._create_chart_placeholder(chart_frame)

    def _create_chart_placeholder(self, parent_frame):
        """创建图表占位符"""
        placeholder_frame = ttk.Frame(parent_frame)
        placeholder_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建居中的提示信息
        info_frame = ttk.Frame(placeholder_frame)
        info_frame.place(relx=0.5, rely=0.5, anchor="center")

        # 图标和文字
        ttk.Label(info_frame, text="📊", font=("Arial", 48)).pack(pady=(0, 10))
        ttk.Label(info_frame, text="图表预览区域",
                 font=("Arial", 16, "bold")).pack(pady=(0, 5))
        ttk.Label(info_frame, text="请选择模型和图表类型，然后点击可视化按钮",
                 font=("Arial", 10), foreground="gray").pack(pady=(0, 10))

        # 功能说明
        features_text = """
✨ 支持的功能:
• 🔍 鼠标滚轮缩放图表
• ↔️ Ctrl+滚轮水平滚动
• 📏 内置测量和标注工具
• 💾 一键保存高清图片
• 🎨 多种图表类型切换
        """
        ttk.Label(info_frame, text=features_text,
                 font=("Arial", 9), foreground="darkblue",
                 justify=tk.LEFT).pack()

    def create_ensemble_tab(self):
        """创建集成学习选项卡"""
        ensemble_tab = ttk.Frame(self.notebook)
        self.notebook.add(ensemble_tab, text="🤝 集成学习")

        # 创建主要的框架
        left_frame = ttk.Frame(ensemble_tab)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        right_frame = ttk.Frame(ensemble_tab)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 单数据源集成学习配置
        single_frame = ttk.LabelFrame(left_frame, text="单数据源集成学习")
        single_frame.pack(fill=tk.X, pady=5)

        # 模型选择（用于单数据源集成）
        model_select_frame = ttk.Frame(single_frame)
        model_select_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(model_select_frame, text="选择模型进行集成:").pack(anchor=tk.W)

        # 创建模型选择的复选框
        model_grid_frame = ttk.Frame(model_select_frame)
        model_grid_frame.pack(fill=tk.X, pady=5)

        # 创建3列的模型选择网格
        for i, model in enumerate(MODEL_NAMES):
            row = i // 3
            col = i % 3
            display_name = MODEL_DISPLAY_NAMES.get(model, model)
            ttk.Checkbutton(model_grid_frame, text=display_name,
                          variable=self.model_vars[model]).grid(row=row, column=col,
                                                               sticky=tk.W, padx=10, pady=2)

        # 集成方法选择
        method_frame = ttk.LabelFrame(single_frame, text="集成方法")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        self.ensemble_methods = {}
        methods = [
            ("voting", "投票法"),
            ("bagging", "装袋法"),
            ("boosting", "提升法"),
            ("stacking", "堆叠法")
        ]

        method_grid_frame = ttk.Frame(method_frame)
        method_grid_frame.pack(fill=tk.X, pady=5)

        for i, (method, display_name) in enumerate(methods):
            var = tk.BooleanVar(value=(method in ['voting', 'stacking']))  # 默认选择常用方法
            self.ensemble_methods[method] = var
            row = i // 2
            col = i % 2
            ttk.Checkbutton(method_grid_frame, text=display_name,
                          variable=var).grid(row=row, column=col, sticky=tk.W, padx=10, pady=2)

        # 智能模型选择配置
        smart_select_frame = ttk.LabelFrame(single_frame, text="智能模型选择")
        smart_select_frame.pack(fill=tk.X, padx=5, pady=5)

        # 选择策略
        strategy_frame = ttk.Frame(smart_select_frame)
        strategy_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(strategy_frame, text="选择策略:").pack(side=tk.LEFT)
        self.selection_strategy_var = tk.StringVar(value="balanced")
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.selection_strategy_var,
                                    values=["performance", "diversity", "balanced", "quantified"],
                                    state="readonly", width=15)
        strategy_combo.pack(side=tk.LEFT, padx=5)

        # 添加策略说明
        ttk.Label(strategy_frame, text="(quantified=量化多样性评估)",
                 font=("Arial", 8)).pack(side=tk.LEFT, padx=5)

        # 目标模型数量
        ttk.Label(strategy_frame, text="目标数量:").pack(side=tk.LEFT, padx=(10,0))
        self.target_size_var = tk.IntVar(value=3)
        ttk.Spinbox(strategy_frame, from_=2, to=8, textvariable=self.target_size_var,
                   width=5).pack(side=tk.LEFT, padx=5)

        # 阈值设置
        threshold_frame = ttk.Frame(smart_select_frame)
        threshold_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(threshold_frame, text="相关性阈值:").pack(side=tk.LEFT)
        self.correlation_threshold_var = tk.DoubleVar(value=0.3)
        ttk.Entry(threshold_frame, textvariable=self.correlation_threshold_var,
                 width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(threshold_frame, text="最低性能:").pack(side=tk.LEFT, padx=(10,0))
        self.min_performance_var = tk.DoubleVar(value=0.6)
        ttk.Entry(threshold_frame, textvariable=self.min_performance_var,
                 width=8).pack(side=tk.LEFT, padx=5)

        # 超参数调优配置
        tuning_frame = ttk.Frame(single_frame)
        tuning_frame.pack(fill=tk.X, padx=5, pady=5)

        self.tune_ensemble_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(tuning_frame, text="启用集成层调参",
                       variable=self.tune_ensemble_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(tuning_frame, text="调参预算:").pack(side=tk.LEFT, padx=(10,0))
        self.tune_budget_var = tk.IntVar(value=30)
        ttk.Entry(tuning_frame, textvariable=self.tune_budget_var,
                 width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(tuning_frame, text="评估指标:").pack(side=tk.LEFT, padx=(10,0))
        self.scoring_var = tk.StringVar(value='f1_weighted')
        scoring_combo = ttk.Combobox(tuning_frame, textvariable=self.scoring_var,
                                   values=['f1_weighted', 'accuracy', 'roc_auc', 'precision_weighted', 'recall_weighted'],
                                   width=12, state='readonly')
        scoring_combo.pack(side=tk.LEFT, padx=5)

        # GPU和并行配置
        performance_frame = ttk.Frame(single_frame)
        performance_frame.pack(fill=tk.X, padx=5, pady=5)

        # 根据实际GPU可用性设置默认值
        try:
            from config import OPTIMIZED_GPU_CONFIG
            gpu_available = OPTIMIZED_GPU_CONFIG.get('use_gpu', False)
            self.use_gpu_var = tk.BooleanVar(value=gpu_available)
        except:
            self.use_gpu_var = tk.BooleanVar(value=False)  # 默认不启用GPU加速
        ttk.Checkbutton(performance_frame, text="启用GPU加速（支持XGBoost/LightGBM/CatBoost）",
                       variable=self.use_gpu_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(performance_frame, text="CPU并行数:").pack(side=tk.LEFT, padx=(10,0))
        self.n_jobs_var = tk.IntVar(value=-1)
        n_jobs_combo = ttk.Combobox(performance_frame, textvariable=self.n_jobs_var,
                                  values=['1', '2', '4', '8', '-1'], width=8, state='readonly')
        n_jobs_combo.pack(side=tk.LEFT, padx=5)

        # 添加提示标签
        ttk.Label(performance_frame, text="(-1表示使用所有CPU核心)",
                 font=('Arial', 8)).pack(side=tk.LEFT, padx=5)

        # 单数据源集成控制按钮
        single_control_frame = ttk.Frame(single_frame)
        single_control_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(single_control_frame, text="🎯 智能模型选择",
                  command=self.smart_model_selection).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="🚀 开始单数据源集成",
                  command=self.start_ensemble).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="📊 集成可视化",
                  command=self.ensemble_visualization).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="💾 导出结果",
                  command=self.export_ensemble_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="📁 打开文件夹",
                  command=self.open_results_folder).pack(side=tk.LEFT, padx=5)

        # 多数据源集成学习配置
        multi_frame = ttk.LabelFrame(left_frame, text="多数据源集成学习")
        multi_frame.pack(fill=tk.X, pady=2)

        # 使用Notebook创建选项卡布局（限制高度）
        self.multi_notebook = ttk.Notebook(multi_frame)
        self.multi_notebook.pack(fill=tk.X, padx=3, pady=3)

        # 设置Notebook的最大高度，确保控制按钮可见
        self.multi_notebook.configure(height=200)

        # 选项卡1：数据源配置
        data_tab = ttk.Frame(self.multi_notebook)
        self.multi_notebook.add(data_tab, text="数据源")

        # 数据源列表
        self.data_sources = {}  # {model_name: data_path}

        # 数据源管理界面
        ds_control_frame = ttk.Frame(data_tab)
        ds_control_frame.pack(fill=tk.X, padx=3, pady=3)

        ttk.Button(ds_control_frame, text="添加", command=self.add_data_source).pack(side=tk.LEFT, padx=2)
        ttk.Button(ds_control_frame, text="删除", command=self.remove_data_source).pack(side=tk.LEFT, padx=2)
        ttk.Button(ds_control_frame, text="清空", command=self.clear_data_sources).pack(side=tk.LEFT, padx=2)

        # 数据源列表显示（减少高度）
        ds_list_frame = ttk.Frame(data_tab)
        ds_list_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # 创建Treeview显示数据源（进一步减少高度）
        columns = ('model', 'data_path')
        self.data_source_tree = ttk.Treeview(ds_list_frame, columns=columns, show='headings', height=3)
        self.data_source_tree.heading('model', text='模型名称')
        self.data_source_tree.heading('data_path', text='数据路径')
        self.data_source_tree.column('model', width=80)
        self.data_source_tree.column('data_path', width=200)

        ds_scrollbar = ttk.Scrollbar(ds_list_frame, orient=tk.VERTICAL, command=self.data_source_tree.yview)
        self.data_source_tree.configure(yscrollcommand=ds_scrollbar.set)

        self.data_source_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ds_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 选项卡2：融合方法配置
        fusion_tab = ttk.Frame(self.multi_notebook)
        self.multi_notebook.add(fusion_tab, text="融合方法")

        # 融合方法选择（使用更简洁的名称）
        self.fusion_methods = {}
        fusion_methods_list = [
            ("hard_voting", "Hard Voting"),
            ("soft_voting", "Soft Voting"),
            ("logit_weighted", "Logit加权"),
            ("confidence_abstain", "Confidence投票"),
            ("stacking", "Stacking")
        ]

        fusion_grid_frame = ttk.Frame(fusion_tab)
        fusion_grid_frame.pack(fill=tk.X, padx=3, pady=2)

        for i, (method, display_name) in enumerate(fusion_methods_list):
            var = tk.BooleanVar(value=(method in ['soft_voting', 'logit_weighted', 'stacking']))
            self.fusion_methods[method] = var
            row = i // 3  # 3列布局更紧凑
            col = i % 3
            ttk.Checkbutton(fusion_grid_frame, text=display_name, variable=var).grid(
                row=row, column=col, sticky=tk.W, padx=3, pady=1
            )

        # 概率校准配置（更紧凑）
        calib_frame = ttk.Frame(fusion_tab)
        calib_frame.pack(fill=tk.X, padx=3, pady=2)

        ttk.Label(calib_frame, text="概率校准:").pack(side=tk.LEFT)
        self.calibration_method_var = tk.StringVar(value="platt")
        calib_combo = ttk.Combobox(calib_frame, textvariable=self.calibration_method_var,
                                 values=["platt", "isotonic", "temperature"], state="readonly", width=10)
        calib_combo.pack(side=tk.LEFT, padx=3)

        # 添加简化的方法说明
        ttk.Label(calib_frame, text="(Platt/等渗/温度)",
                 font=("Arial", 8)).pack(side=tk.LEFT, padx=3)

        # 选项卡3：配置管理
        config_tab = ttk.Frame(self.multi_notebook)
        self.multi_notebook.add(config_tab, text="配置管理")

        # 配置文件管理
        config_frame = ttk.LabelFrame(config_tab, text="配置文件")
        config_frame.pack(fill=tk.X, padx=3, pady=3)

        config_input_frame = ttk.Frame(config_frame)
        config_input_frame.pack(fill=tk.X, padx=3, pady=3)

        self.config_path_var = tk.StringVar()
        ttk.Entry(config_input_frame, textvariable=self.config_path_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(config_input_frame, text="浏览", command=self.browse_config_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(config_input_frame, text="生成", command=self.generate_config).pack(side=tk.LEFT, padx=2)

        # 添加简化的说明文本（进一步减少高度）
        info_frame = ttk.LabelFrame(config_tab, text="使用说明")
        info_frame.pack(fill=tk.X, padx=3, pady=3)

        info_text = tk.Text(info_frame, height=3, wrap=tk.WORD, font=("Arial", 9))
        info_text.pack(fill=tk.X, padx=3, pady=2)

        help_content = """决策层融合：各模态独立训练，预测阶段融合
推荐：Soft Voting(直观)、Logit加权(理论强)、Stacking(性能佳)
支持概率校准和双层SHAP解释分析"""

        info_text.insert(tk.END, help_content)
        info_text.config(state=tk.DISABLED)

        # 初始化兼容性变量（保持向后兼容）
        self.data_strategies = {"unified": tk.BooleanVar(value=True)}
        self.feature_selection_var = tk.BooleanVar(value=False)  # 禁用特征选择
        self.feature_method_var = tk.StringVar(value="weighted")
        self.feature_k_var = tk.IntVar(value=10)

        # 多数据源集成控制按钮（放在主框架底部）
        multi_control_frame = ttk.Frame(multi_frame)
        multi_control_frame.pack(fill=tk.X, padx=3, pady=5)

        # 使用更紧凑的按钮布局
        ttk.Button(multi_control_frame, text="🔗 开始集成学习",
                  command=self.start_enhanced_multi_data_ensemble).pack(side=tk.LEFT, padx=2)
        ttk.Button(multi_control_frame, text="📊 结果可视化",
                  command=self.visualize_ensemble_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(multi_control_frame, text="🔍 SHAP分析",
                  command=self.view_ensemble_shap).pack(side=tk.LEFT, padx=2)

        # 添加传统多数据源集成学习按钮（兼容性）
        ttk.Button(multi_control_frame, text="📋 传统集成",
                  command=self.start_multi_data_ensemble).pack(side=tk.RIGHT, padx=2)

        # 其他功能控制
        other_frame = ttk.LabelFrame(left_frame, text="其他功能")
        other_frame.pack(fill=tk.X, pady=5)

        other_control_frame = ttk.Frame(other_frame)
        other_control_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(other_control_frame, text="� 外部验证",
                  command=self.external_validation).pack(side=tk.LEFT, padx=5)
        ttk.Button(other_control_frame, text="� 查看结果",
                  command=self.view_ensemble_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(other_control_frame, text="�️ 打开输出目录",
                  command=self.open_output_directory).pack(side=tk.LEFT, padx=5)

        # 右侧：进度显示和结果显示
        # 进度显示
        progress_frame = ttk.LabelFrame(right_frame, text="执行进度")
        progress_frame.pack(fill=tk.X, pady=5)

        self.ensemble_progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.ensemble_progress.pack(fill=tk.X, padx=5, pady=5)

        self.ensemble_status_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.ensemble_status_var).pack(pady=2)

        # 集成结果显示
        result_frame = ttk.LabelFrame(right_frame, text="集成结果与日志")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建带标签页的结果显示
        result_notebook = ttk.Notebook(result_frame)
        result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 结果标签页
        result_tab = ttk.Frame(result_notebook)
        result_notebook.add(result_tab, text="执行结果")

        self.ensemble_result_text = tk.Text(result_tab, wrap=tk.WORD, font=('Consolas', 9))
        result_scrollbar = ttk.Scrollbar(result_tab, orient=tk.VERTICAL, command=self.ensemble_result_text.yview)
        self.ensemble_result_text.configure(yscrollcommand=result_scrollbar.set)

        self.ensemble_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 性能对比标签页
        performance_tab = ttk.Frame(result_notebook)
        result_notebook.add(performance_tab, text="性能对比")

        self.performance_text = tk.Text(performance_tab, wrap=tk.WORD, font=('Consolas', 9))
        perf_scrollbar = ttk.Scrollbar(performance_tab, orient=tk.VERTICAL, command=self.performance_text.yview)
        self.performance_text.configure(yscrollcommand=perf_scrollbar.set)

        self.performance_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化显示信息
        self.show_single_ensemble_info()

    # 导航事件处理
    def on_nav_select(self, _):
        """处理导航树选择事件"""
        selection = self.nav_tree.selection()
        if selection:
            item = selection[0]
            tags = self.nav_tree.item(item, "tags")
            if tags:
                tag = tags[0]
                # 根据标签切换到相应的选项卡
                if tag in ["load_data", "preview_data", "validate_data", "preprocess_data"]:
                    self.notebook.select(0)  # 数据管理选项卡
                elif tag == "data_exploration":
                    self.notebook.select(4)  # 数据探索选项卡
                elif tag in ["select_models", "config_params", "start_training", "hyperparameter_tuning"]:
                    self.notebook.select(1)  # 模型训练选项卡
                elif tag in ["single_viz", "compare_viz", "performance_report"]:
                    self.notebook.select(2)  # 结果可视化选项卡
                elif tag in ["single_ensemble", "multi_ensemble", "external_validation"]:
                    self.notebook.select(3)  # 集成学习选项卡
                    # 根据具体选择显示相应功能
                    if tag == "single_ensemble":
                        self.show_single_ensemble_info()
                    elif tag == "multi_ensemble":
                        self.show_multi_ensemble_info()
                    elif tag == "external_validation":
                        self.show_external_validation_info()

    # 数据管理相关方法
    def browse_data_file(self):
        """浏览数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.current_data_path.set(file_path)

    def generate_config(self):
        """生成多数据源配置文件"""
        # 创建配置生成对话框
        config_window = tk.Toplevel(self.root)
        config_window.title("生成多数据源配置")
        config_window.geometry("600x400")
        config_window.transient(self.root)
        config_window.grab_set()

        # 配置映射列表
        ttk.Label(config_window, text="模型-数据源映射配置:").pack(pady=5)

        # 创建配置表格
        columns = ("模型", "数据文件路径")
        config_tree = ttk.Treeview(config_window, columns=columns, show="headings", height=10)

        for col in columns:
            config_tree.heading(col, text=col)
            config_tree.column(col, width=250)

        config_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(config_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def add_mapping():
            """添加模型-数据映射"""
            add_window = tk.Toplevel(config_window)
            add_window.title("添加映射")
            add_window.geometry("400x150")
            add_window.transient(config_window)
            add_window.grab_set()

            ttk.Label(add_window, text="模型:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
            model_var = tk.StringVar()
            model_combo = ttk.Combobox(add_window, textvariable=model_var, values=MODEL_NAMES, state="readonly")
            model_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)

            ttk.Label(add_window, text="数据文件:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
            data_var = tk.StringVar()
            data_entry = ttk.Entry(add_window, textvariable=data_var, width=30)
            data_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)

            def browse_data():
                file_path = filedialog.askopenfilename(
                    title="选择数据文件",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
                if file_path:
                    data_var.set(file_path)

            ttk.Button(add_window, text="浏览...", command=browse_data).grid(row=1, column=2, padx=5, pady=5)

            def confirm_add():
                if model_var.get() and data_var.get():
                    config_tree.insert("", "end", values=(model_var.get(), data_var.get()))
                    add_window.destroy()
                else:
                    messagebox.showerror("错误", "请填写完整信息")

            ttk.Button(add_window, text="确定", command=confirm_add).grid(row=2, column=1, padx=5, pady=10)
            add_window.columnconfigure(1, weight=1)

        def remove_mapping():
            """删除选中的映射"""
            selected = config_tree.selection()
            if selected:
                for item in selected:
                    config_tree.delete(item)

        def save_config():
            """保存配置文件"""
            items = config_tree.get_children()
            if not items:
                messagebox.showerror("错误", "请至少添加一个模型-数据映射")
                return

            # 构建配置字典
            model_data_mapping = {}
            for item in items:
                values = config_tree.item(item)['values']
                model_data_mapping[values[0]] = values[1]

            config_data = {
                "model_data_mapping": model_data_mapping,
                "created_time": datetime.now().isoformat(),
                "description": "多数据源集成学习配置文件"
            }

            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if save_path:
                try:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, indent=2, ensure_ascii=False)

                    self.config_path_var.set(save_path)
                    messagebox.showinfo("成功", f"配置文件已保存到:\n{save_path}")
                    config_window.destroy()

                except Exception as e:
                    messagebox.showerror("错误", f"保存配置文件失败:\n{str(e)}")

        ttk.Button(button_frame, text="添加映射", command=add_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除映射", command=remove_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置", command=save_config).pack(side=tk.RIGHT, padx=5)

    def load_data_file(self):
        """加载数据文件"""
        data_path = self.current_data_path.get()
        if not data_path:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        try:
            # 使用数据预处理器加载数据
            from code.data_preprocessing import load_and_clean_data

            self.status_text.set("正在加载数据...")
            self.root.update()

            # 记录数据加载开始时间
            load_start = datetime.now()
            self.log_message(f"开始加载数据文件: {data_path}")

            # 加载数据
            df, target_col = load_and_clean_data(data_path)

            # 检查返回值是否有效
            if df is None:
                raise ValueError("数据加载失败，返回空值")

            # 更新数据预览
            self.update_data_preview(df)

            # 更新数据信息
            self.update_data_info(df, target_col)

            # 记录加载完成时间
            self.log_performance("数据加载", load_start)
            self.status_text.set(f"数据加载成功: {df.shape[0]} 行, {df.shape[1]} 列")
            self.log_message(f"成功加载数据文件: {data_path} - {df.shape[0]}行, {df.shape[1]}列")

            # 更新配置显示
            self.refresh_config_display()

        except FileNotFoundError:
            messagebox.showerror("错误", f"数据文件未找到: {data_path}")
            self.status_text.set("数据文件未找到")
            self.log_message(f"数据文件未找到: {data_path}")
            self.refresh_config_display()
        except PermissionError:
            messagebox.showerror("错误", f"没有权限访问数据文件: {data_path}")
            self.status_text.set("权限错误")
            self.log_message(f"权限错误: {data_path}")
            self.refresh_config_display()
        except pd.errors.EmptyDataError:
            messagebox.showerror("错误", "数据文件为空")
            self.status_text.set("数据文件为空")
            self.log_message("数据文件为空")
            self.refresh_config_display()
        except pd.errors.ParserError:
            messagebox.showerror("错误", "数据文件格式错误，请检查CSV格式")
            self.status_text.set("数据格式错误")
            self.log_message("数据文件格式错误")
            self.refresh_config_display()
        except ValueError as e:
            if "数据加载失败，返回空值" in str(e):
                messagebox.showerror("错误", "数据加载失败，请检查数据文件格式")
                self.status_text.set("数据加载失败")
                self.log_message("数据加载失败: 返回空值")
            else:
                messagebox.showerror("错误", f"数据值错误: {e}")
                self.status_text.set("数据值错误")
                self.log_message(f"数据值错误: {e}")
            self.refresh_config_display()
        except ImportError as e:
            messagebox.showerror("错误", f"导入模块失败: {e}")
            self.status_text.set("模块导入失败")
            self.log_message(f"模块导入失败: {e}")
            self.refresh_config_display()
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")
            self.status_text.set("数据加载失败")
            self.log_message(f"数据加载失败: {e}")
            self.refresh_config_display()

    def update_data_preview(self, df):
        """更新数据预览表格"""
        # 清除现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        # 设置列
        columns = list(df.columns)
        self.data_tree["columns"] = columns
        self.data_tree["show"] = "headings"

        # 设置列标题
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)

        # 插入数据（只显示前100行）
        for _, row in df.head(100).iterrows():
            self.data_tree.insert("", "end", values=list(row))

    def update_data_info(self, df, target_col):
        """更新数据信息显示"""
        info_text = f"数据形状: {df.shape[0]} 行 × {df.shape[1]} 列\n"
        info_text += f"目标列: {target_col}\n"
        info_text += f"特征列数: {df.shape[1] - 1}\n\n"

        # 数据类型信息
        info_text += "数据类型:\n"
        for col, dtype in df.dtypes.items():
            info_text += f"  {col}: {dtype}\n"

        # 缺失值信息
        missing = df.isnull().sum()
        if missing.sum() > 0:
            info_text += "\n缺失值:\n"
            for col, count in missing.items():
                if count > 0:
                    info_text += f"  {col}: {count}\n"
        else:
            info_text += "\n无缺失值\n"

        # 目标变量分布
        if target_col in df.columns:
            target_counts = df[target_col].value_counts()
            info_text += "\n目标变量分布:\n"
            for value, count in target_counts.items():
                info_text += f"  {value}: {count}\n"

        self.data_info_text.delete(1.0, tk.END)
        self.data_info_text.insert(1.0, info_text)

    # 模型训练相关方法
    def select_all_models(self):
        """全选所有模型"""
        for var in self.model_vars.values():
            var.set(True)
        # 配置会通过trace自动更新

    def deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
        # 配置会通过trace自动更新

    def recommend_models(self):
        """推荐模型选择"""
        # 简单的推荐逻辑：选择几个常用的高性能模型
        recommended = ["RandomForest", "XGBoost", "LightGBM", "Logistic"]

        # 先取消所有选择
        self.deselect_all_models()

        # 选择推荐的模型
        for model in recommended:
            if model in self.model_vars:
                self.model_vars[model].set(True)

        messagebox.showinfo("推荐", f"已选择推荐模型: {', '.join(recommended)}")
        # 配置会通过trace自动更新

    def update_test_size_label(self, value):
        """更新测试集比例标签"""
        self.test_size_label.config(text=f"{float(value):.2f}")
        # 同时更新配置显示
        if hasattr(self, 'config_test_size_var'):
            self.config_test_size_var.set(f"{float(value):.2f}")
        # 同步到训练配置选项卡
        if hasattr(self, 'training_test_size_var'):
            self.training_test_size_var.set(float(value))

    def _sync_test_size_from_training_config(self, *args):
        """从训练配置选项卡同步测试集比例到主参数"""
        # args 参数用于 tkinter 变量追踪回调，这里不需要使用
        _ = args  # 明确标记参数已被处理
        try:
            if hasattr(self, 'test_size_var') and hasattr(self, 'training_test_size_var'):
                new_value = self.training_test_size_var.get()
                # 避免循环更新
                if abs(self.test_size_var.get() - new_value) > 0.001:
                    self.test_size_var.set(new_value)
        except Exception as e:
            if hasattr(self, 'log_message'):
                self.log_message(f"同步测试集比例失败: {e}")

    def _sync_test_size_to_training_config(self, *args):
        """从主参数同步测试集比例到训练配置选项卡"""
        # args 参数用于 tkinter 变量追踪回调，这里不需要使用
        _ = args  # 明确标记参数已被处理
        try:
            if hasattr(self, 'test_size_var') and hasattr(self, 'training_test_size_var'):
                new_value = self.test_size_var.get()
                # 避免循环更新
                if abs(self.training_test_size_var.get() - new_value) > 0.001:
                    self.training_test_size_var.set(new_value)
        except Exception as e:
            if hasattr(self, 'log_message'):
                self.log_message(f"同步测试集比例失败: {e}")

    def get_selected_models(self):
        """获取选中的模型列表"""
        selected = []
        for model_name, var in self.model_vars.items():
            if var.get():
                selected.append(model_name)
        return selected

    def toggle_tuning_options(self):
        """切换超参数调优选项的启用状态"""
        enabled = self.enable_tuning_var.get()
        # 这里可以添加启用/禁用相关控件的逻辑
        if enabled:
            self.log_message("已启用超参数调优")
        else:
            self.log_message("已禁用超参数调优")

        # 触发配置更新
        self.schedule_config_update()

    def toggle_early_stopping_options(self):
        """切换早停机制选项的启用状态"""
        enabled = self.enable_early_stopping_var.get()
        if enabled:
            self.log_message("已启用早停机制")
        else:
            self.log_message("已禁用早停机制")

    def toggle_strict_reproducibility(self):
        """切换严格复现模式选项的启用状态"""
        enabled = self.strict_reproducibility_var.get()

        # 启用/禁用详细设置控件
        state = tk.NORMAL if enabled else tk.DISABLED
        for widget in self.repro_detail_widgets:
            widget.config(state=state)

        if enabled:
            self.log_message("已启用严格复现模式")
            # 应用严格复现模式设置
            try:
                from config import REPRODUCIBILITY_CONFIG, apply_reproducibility_env
                REPRODUCIBILITY_CONFIG['strict'] = True
                REPRODUCIBILITY_CONFIG['num_threads'] = self.repro_num_threads_var.get()
                REPRODUCIBILITY_CONFIG['enforce_cpu'] = self.repro_enforce_cpu_var.get()
                apply_reproducibility_env(strict=True)
                self.log_message("严格复现模式环境设置已应用")
            except Exception as e:
                self.log_message(f"应用严格复现模式设置失败: {e}")
        else:
            self.log_message("已禁用严格复现模式")
            # 重置严格复现模式设置
            try:
                from config import REPRODUCIBILITY_CONFIG
                REPRODUCIBILITY_CONFIG['strict'] = False
            except Exception as e:
                self.log_message(f"重置严格复现模式设置失败: {e}")

        # 触发配置更新
        self.schedule_config_update()

    def get_tuning_config(self):
        """获取超参数调优配置"""
        return {
            'enabled': self.enable_tuning_var.get(),
            'n_trials': self.n_trials_var.get(),
            'strategy': self.tuning_strategy_var.get(),
            'timeout': self.tuning_timeout_var.get() * 60,  # 转换为秒
            'n_jobs': self.tuning_n_jobs_var.get(),
            'metric': self.tuning_metric_var.get(),
            'early_stopping': self.enable_early_stopping_var.get(),
            'patience': self.early_stopping_patience_var.get(),
            'min_improvement': self.min_improvement_var.get()
        }

    def get_reproducibility_config(self):
        """获取严格复现模式配置"""
        return {
            'strict_reproducibility': self.strict_reproducibility_var.get(),
            'num_threads': self.repro_num_threads_var.get(),
            'enforce_cpu': self.repro_enforce_cpu_var.get()
        }

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 添加到系统日志
        self.logger.info(message)

        # 添加到训练日志（线程安全）
        self.update_gui_safely(self.training_log, 'insert', tk.END, log_entry)
        self.update_gui_safely(self.training_log, 'see', tk.END)

        # 添加到右侧日志面板（线程安全）
        self.update_gui_safely(self.log_text, 'insert', tk.END, log_entry)
        self.update_gui_safely(self.log_text, 'see', tk.END)

    def log_performance(self, operation, start_time, end_time=None):
        """记录性能日志"""
        if end_time is None:
            end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        self.logger.info(f"性能统计 - {operation}: {duration:.3f}秒")
        
        # 如果操作耗时过长，记录警告
        if duration > 5.0:  # 超过5秒
            self.logger.warning(f"操作耗时过长 - {operation}: {duration:.3f}秒")

    def update_gui_safely(self, widget, method, *args):
        """线程安全的GUI更新方法"""
        try:
            self.root.after(0, lambda: getattr(widget, method)(*args))
        except tk.TclError:
            # GUI已关闭，忽略更新
            pass

    def show_message_safely(self, msg_type, title, message):
        """线程安全的消息框显示方法"""
        def show_msg():
            try:
                if msg_type == "info":
                    messagebox.showinfo(title, message)
                elif msg_type == "error":
                    messagebox.showerror(title, message)
                elif msg_type == "warning":
                    messagebox.showwarning(title, message)
            except tk.TclError:
                # GUI已关闭，忽略消息
                pass

        try:
            self.root.after(0, show_msg)
        except tk.TclError:
            # GUI已关闭，忽略消息
            pass

    def schedule_config_update(self):
        """Debounced config update to prevent excessive updates"""
        if self.config_update_timer is not None:
            self.root.after_cancel(self.config_update_timer)
        
        # Wait 300ms before updating to batch rapid changes
        self.config_update_timer = self.root.after(300, self._perform_config_update)

    def _perform_config_update(self):
        """执行配置更新"""
        self.refresh_config_display()

    def refresh_config_display(self):
        """刷新配置显示"""
        try:
            # 检查必要的变量是否存在
            if not hasattr(self, 'data_status_var'):
                return

            # 更新数据状态
            data_path = self.current_data_path.get()
            if data_path:
                self.data_status_var.set("已加载数据")
            else:
                self.data_status_var.set("未加载数据")

            # 更新选中的模型
            selected_models = self.get_selected_models()
            if selected_models:
                models_text = ", ".join([MODEL_DISPLAY_NAMES.get(m, m) or m for m in selected_models])
                if len(models_text) > 40:
                    models_text = models_text[:37] + "..."
                self.selected_models_var.set(models_text)
            else:
                self.selected_models_var.set("未选择模型")

            # 更新训练参数
            self.config_test_size_var.set(f"{self.test_size_var.get():.2f}")
            self.config_seed_var.set(str(self.random_seed_var.get()))
            self.config_scaling_var.set(self.scaling_var.get())

            # 更新超参数调优配置
            if self.enable_tuning_var.get():
                self.config_tuning_status_var.set("已启用")
            else:
                self.config_tuning_status_var.set("未启用")

            self.config_trials_var.set(str(self.n_trials_var.get()))
            self.config_strategy_var.set(self.tuning_strategy_var.get())

            # 更新系统状态
            try:
                import psutil
                memory_percent = psutil.virtual_memory().percent
                self.config_memory_var.set(f"{memory_percent:.1f}%")
            except ImportError:
                self.config_memory_var.set("未知")

        except Exception as e:
            if hasattr(self, 'log_message'):
                self.log_message(f"刷新配置显示失败: {e}")

    def save_current_config(self):
        """保存当前配置"""
        try:
            config_data = {
                "data_path": self.current_data_path.get(),
                "selected_models": self.get_selected_models(),
                "training_params": {
                    "test_size": self.test_size_var.get(),
                    "random_seed": self.random_seed_var.get(),
                    "scaling_method": self.scaling_var.get()
                },
                "tuning_config": self.get_tuning_config(),
                "reproducibility_config": {
                    "strict_reproducibility": self.strict_reproducibility_var.get(),
                    "num_threads": self.repro_num_threads_var.get(),
                    "enforce_cpu": self.repro_enforce_cpu_var.get()
                },
                "created_time": datetime.now().isoformat(),
                "description": "GUI配置文件"
            }

            file_path = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配置已保存到:\n{file_path}")
                self.log_message(f"配置已保存: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")
            self.log_message(f"保存配置失败: {e}")

    def load_config_file(self):
        """加载配置文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="加载配置文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 应用配置
                if "data_path" in config_data:
                    self.current_data_path.set(config_data["data_path"])

                if "selected_models" in config_data:
                    # 先取消所有选择
                    self.deselect_all_models()
                    # 选择配置中的模型
                    for model in config_data["selected_models"]:
                        if model in self.model_vars:
                            self.model_vars[model].set(True)

                if "training_params" in config_data:
                    params = config_data["training_params"]
                    if "test_size" in params:
                        self.test_size_var.set(params["test_size"])
                        # 同步到训练配置选项卡
                        if hasattr(self, 'training_test_size_var'):
                            self.training_test_size_var.set(params["test_size"])
                    if "random_seed" in params:
                        self.random_seed_var.set(params["random_seed"])
                    if "scaling_method" in params:
                        self.scaling_var.set(params["scaling_method"])

                if "tuning_config" in config_data:
                    tuning = config_data["tuning_config"]
                    if "enabled" in tuning:
                        self.enable_tuning_var.set(tuning["enabled"])
                    if "n_trials" in tuning:
                        self.n_trials_var.set(tuning["n_trials"])
                    if "strategy" in tuning:
                        self.tuning_strategy_var.set(tuning["strategy"])

                # 加载严格复现模式配置
                if "reproducibility_config" in config_data:
                    repro = config_data["reproducibility_config"]
                    if "strict_reproducibility" in repro:
                        self.strict_reproducibility_var.set(repro["strict_reproducibility"])
                    if "num_threads" in repro:
                        self.repro_num_threads_var.set(repro["num_threads"])
                    if "enforce_cpu" in repro:
                        self.repro_enforce_cpu_var.set(repro["enforce_cpu"])

                    # 如果启用了严格复现模式，应用设置
                    if repro.get("strict_reproducibility", False):
                        self.toggle_strict_reproducibility()

                # 刷新配置显示
                self.refresh_config_display()

                messagebox.showinfo("成功", f"配置已从以下文件加载:\n{file_path}")
                self.log_message(f"配置已加载: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败:\n{str(e)}")
            self.log_message(f"加载配置失败: {e}")

    # 委托给功能模块的方法
    def load_data(self):
        """加载数据"""
        if hasattr(self, 'functions'):
            self.functions.load_data()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def start_training(self):
        """开始训练"""
        if hasattr(self, 'functions'):
            self.functions.start_training()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def stop_training(self):
        """停止训练"""
        if hasattr(self, 'functions'):
            self.functions.stop_training()
        else:
            messagebox.showwarning("警告", "GUI功能模块未初始化")

    def single_model_visualization(self):
        """单模型可视化"""
        if hasattr(self, 'functions'):
            self.functions.single_model_visualization()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def model_comparison(self):
        """模型比较"""
        if hasattr(self, 'functions'):
            self.functions.model_comparison()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def refresh_chart(self):
        """刷新图表"""
        if hasattr(self, 'functions'):
            self.functions.refresh_chart()
        else:
            messagebox.showwarning("警告", "GUI功能模块未初始化")

    def save_chart(self):
        """保存图表"""
        if hasattr(self, 'functions'):
            self.functions.save_chart()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def generate_detailed_report(self):
        """生成详细报告"""
        if hasattr(self, 'functions'):
            self.functions.generate_detailed_report()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def select_best_model(self):
        """自动选择最佳模型"""
        if hasattr(self, 'functions'):
            self.functions.select_best_model_automatically()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def run_complete_analysis(self):
        """运行完整分析"""
        if hasattr(self, 'functions'):
            self.functions.run_complete_analysis()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    # 其他功能方法的简单实现
    def save_project(self):
        """保存项目"""
        file_path = filedialog.asksaveasfilename(
            title="保存项目",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                project_data = {
                    'data_path': self.current_data_path.get(),
                    'selected_models': self.get_selected_models(),
                    'test_size': self.test_size_var.get(),
                    'random_seed': self.random_seed_var.get(),
                    'scaling_method': self.scaling_var.get()
                }
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", f"项目已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存项目失败: {e}")

    def load_project(self):
        """加载项目"""
        file_path = filedialog.askopenfilename(
            title="加载项目",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # 恢复项目设置
                self.current_data_path.set(project_data.get('data_path', ''))
                test_size = project_data.get('test_size', 0.2)
                self.test_size_var.set(test_size)
                # 同步到训练配置选项卡
                if hasattr(self, 'training_test_size_var'):
                    self.training_test_size_var.set(test_size)
                self.random_seed_var.set(project_data.get('random_seed', 42))
                self.scaling_var.set(project_data.get('scaling_method', 'standard'))

                # 恢复模型选择
                selected_models = project_data.get('selected_models', [])
                for model_name, var in self.model_vars.items():
                    var.set(model_name in selected_models)

                messagebox.showinfo("成功", f"项目已从 {file_path} 加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载项目失败: {e}")

    def export_results(self):
        """导出结果"""
        if (not hasattr(self, 'functions') or
            not hasattr(self.functions, 'trained_models') or
            not self.functions.trained_models):
            messagebox.showwarning("警告", "没有可导出的结果，请先训练模型")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出结果",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            try:
                # 创建结果摘要
                results = []
                for model_name in self.functions.trained_models:
                    results.append({
                        'Model': model_name,
                        'Status': 'Trained',
                        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })

                import pandas as pd
                df = pd.DataFrame(results)
                df.to_csv(file_path, index=False)
                messagebox.showinfo("成功", f"结果已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出结果失败: {e}")

    def quit_app(self):
        """退出应用"""
        if messagebox.askokcancel("退出", "确定要退出应用吗？"):
            self.on_closing()

    def clear_cache(self):
        """清除缓存"""
        if messagebox.askokcancel("清除缓存", "确定要清除所有缓存文件吗？"):
            try:
                import shutil
                if CACHE_PATH.exists():
                    shutil.rmtree(CACHE_PATH)
                    CACHE_PATH.mkdir(parents=True, exist_ok=True)
                messagebox.showinfo("成功", "缓存已清除")
            except Exception as e:
                messagebox.showerror("错误", f"清除缓存失败: {e}")

    def reset_settings(self):
        """重置设置"""
        if messagebox.askokcancel("重置设置", "确定要重置所有设置吗？"):
            self.current_data_path.set("")
            self.test_size_var.set(0.2)
            self.random_seed_var.set(42)
            self.scaling_var.set("standard")
            self.deselect_all_models()
            messagebox.showinfo("成功", "设置已重置")

    def check_results(self):
        """检查任务结果（定时器）"""
        if not self.is_running:
            return
        try:
            # 这里可以添加定期检查任务状态的逻辑

            # 取消之前的定时器（如果存在）
            if self.check_results_timer_id is not None:
                try:
                    self.root.after_cancel(self.check_results_timer_id)
                except tk.TclError:
                    pass

            # 设置新的定时器
            self.check_results_timer_id = self.root.after(1000, self.check_results)  # 每秒检查一次
        except tk.TclError:
            # GUI已关闭，停止定时器
            self.is_running = False
            self.check_results_timer_id = None

    def on_closing(self):
        """处理窗口关闭事件"""
        self.is_running = False

        # 取消TimerManager管理的所有定时器
        if hasattr(self, 'timer_manager'):
            try:
                self.timer_manager.cancel_all()
            except Exception:
                pass

        # 取消旧的定时器列表（保持向后兼容）
        for timer_id in self.timer_ids:
            try:
                self.root.after_cancel(timer_id)
            except tk.TclError:
                pass

        # 取消 check_results 定时器
        if self.check_results_timer_id is not None:
            try:
                self.root.after_cancel(self.check_results_timer_id)
            except tk.TclError:
                pass
            self.check_results_timer_id = None

        # 取消 update_time 定时器
        if self.update_time_timer_id is not None:
            try:
                self.root.after_cancel(self.update_time_timer_id)
            except tk.TclError:
                pass
            self.update_time_timer_id = None

        # 取消配置更新定时器
        if hasattr(self, 'config_update_timer') and self.config_update_timer is not None:
            try:
                self.root.after_cancel(self.config_update_timer)
            except tk.TclError:
                pass
            self.config_update_timer = None

        # 停止所有正在运行的训练线程
        if hasattr(self, 'functions') and hasattr(self.functions, 'is_training'):
            self.functions.is_training = False
        
        # 停止后台任务线程
        if hasattr(self, 'task_queue'):
            # 清空任务队列
            try:
                while not self.task_queue.empty():
                    self.task_queue.get_nowait()
            except:
                pass

        # 清理会话管理器
        if hasattr(self, 'session_manager_gui') and self.session_manager_gui:
            try:
                # 关闭所有会话相关资源
                if hasattr(self.session_manager_gui, 'close_all_sessions'):
                    self.session_manager_gui.close_all_sessions()
            except Exception:
                pass

        # 清理绘图管理器
        if hasattr(self, 'plot_manager'):
            try:
                # 关闭所有matplotlib图形
                import matplotlib.pyplot as plt
                plt.close('all')
            except Exception:
                pass

        # 清理数据预处理器
        if hasattr(self, 'data_preprocessor'):
            try:
                # 清理数据预处理器资源
                if hasattr(self.data_preprocessor, 'cleanup'):
                    self.data_preprocessor.cleanup()
            except Exception:
                pass

        # 记录关闭日志
        if hasattr(self, 'logger'):
            try:
                self.logger.info("应用程序正在关闭...")
            except Exception:
                pass

        # 关闭窗口
        try:
            self.root.quit()
            self.root.destroy()
        except tk.TclError:
            # 窗口可能已经关闭
            pass

    # 占位方法（暂未实现的功能）
    def train_models(self):
        self.start_training()

    def visualize_results(self):
        self.single_model_visualization()

    def compare_models(self):
        self.model_comparison()

    def hyperparameter_tuning(self):
        self.start_hyperparameter_tuning()

    def start_hyperparameter_tuning(self):
        if hasattr(self, 'functions'):
            self.functions.start_hyperparameter_tuning()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def show_tuning_results(self):
        if hasattr(self, 'functions'):
            self.functions.show_tuning_results()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")
    def ensemble_learning(self):
        """开始集成学习"""
        if not self.current_data_path.get():
            messagebox.showerror("错误", "请先选择数据文件")
            return

        # 获取选中的模型
        selected_models = [model for model, var in self.model_vars.items() if var.get()]
        if len(selected_models) < 2:
            messagebox.showerror("错误", "集成学习需要至少选择2个模型")
            return

        # 获取选中的集成方法
        selected_methods = [method for method, var in self.ensemble_methods.items() if var.get()]
        if not selected_methods:
            messagebox.showerror("错误", "请至少选择一种集成方法")
            return

        # 开始进度显示
        self.ensemble_progress.start()
        self.ensemble_status_var.set("正在执行集成学习...")

        def run_ensemble():
            try:
                self.update_gui_safely(self.ensemble_result_text, 'delete', '1.0', tk.END)
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "正在加载数据...\n")

                # 加载数据
                X_train, X_test, y_train, y_test = load_and_preprocess_data(self.current_data_path.get())

                # 获取当前使用的scaler，传递给模型训练器
                from data_preprocessing import get_current_scaler
                current_scaler = get_current_scaler()

                if current_scaler is not None:
                    # 为所有选中的模型设置scaler
                    for model_name in selected_models:
                        if model_name in MODEL_TRAINERS:
                            setattr(MODEL_TRAINERS[model_name], '_scaler', current_scaler)

                # 安全获取数据形状信息
                train_shape = getattr(X_train, 'shape', f"({len(X_train)} 样本)")
                test_shape = getattr(X_test, 'shape', f"({len(X_test)} 样本)")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"数据加载完成: 训练集{train_shape}, 测试集{test_shape}\n")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"开始集成学习，模型: {', '.join(selected_models)}\n")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"集成方法: {', '.join(selected_methods)}\n")

                # 显示调参配置
                tune_enabled = self.tune_ensemble_var.get()
                use_gpu = self.use_gpu_var.get()
                n_jobs = self.n_jobs_var.get()

                if tune_enabled:
                    tune_budget = self.tune_budget_var.get()
                    scoring = self.scoring_var.get()
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"启用超参数调优: 预算={tune_budget}, 指标={scoring}\n")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "使用默认参数（未启用调优）\n")

                # 显示性能配置
                if use_gpu:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "启用GPU加速（XGBoost/LightGBM/CatBoost）\n")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "使用CPU模式\n")

                if n_jobs == -1:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "使用所有CPU核心并行\n")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"使用{n_jobs}个CPU核心并行\n")

                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n")

                # 运行集成学习
                ensemble_results = run_ensemble_pipeline(
                    X_train=X_train,
                    y_train=y_train,
                    X_test=X_test,
                    y_test=y_test,
                    model_names=selected_models,
                    ensemble_methods=selected_methods,
                    save_results=True,
                    output_dir=OUTPUT_PATH / 'ensemble',
                    enable_shap=True,
                    tune_ensemble=tune_enabled,
                    tune_budget=self.tune_budget_var.get(),
                    scoring=self.scoring_var.get(),
                    use_gpu=use_gpu,
                    n_jobs=n_jobs
                )

                if ensemble_results:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "集成学习完成！\n\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "=== 集成结果 ===\n")

                    # 显示结果
                    performance_data = []
                    for name, result in ensemble_results.items():
                        metrics = result['metrics']
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n{name}:\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  准确率: {metrics['accuracy']:.4f}\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  精确率: {metrics['precision']:.4f}\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  召回率: {metrics['recall']:.4f}\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  F1分数: {metrics['f1_score']:.4f}\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  AUC: {metrics['auc']:.4f}\n")

                        # 收集性能数据用于对比显示
                        performance_data.append((name, metrics))

                    # 找出最佳模型
                    best_model = max(ensemble_results.keys(),
                                   key=lambda x: ensemble_results[x]['metrics']['f1_score'])
                    best_metrics = ensemble_results[best_model]['metrics']

                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n🏆 最佳集成模型: {best_model}\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"   F1分数: {best_metrics['f1_score']:.4f}\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"   准确率: {best_metrics['accuracy']:.4f}\n")

                    # 更新性能对比显示
                    self.update_performance_comparison(performance_data)

                    # 保存结果供可视化使用
                    self.current_ensemble_results = ensemble_results
                    self.current_ensemble_data = (X_train, y_train, X_test, y_test)

                    # 如果有多个集成模型，进行DeLong检验
                    if len(ensemble_results) > 1:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n正在进行DeLong检验比较集成模型性能...\n")
                        try:
                            self._perform_ensemble_delong_test(ensemble_results, X_test, y_test)
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "DeLong检验完成\n")
                        except Exception as e:
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"DeLong检验失败: {e}\n")

                    # 停止进度显示
                    self.ensemble_progress.stop()
                    self.ensemble_status_var.set("集成学习完成")

                    self.show_message_safely("info", "成功", "集成学习完成！可以查看可视化结果。")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "集成学习失败！\n")
                    self.ensemble_progress.stop()
                    self.ensemble_status_var.set("集成学习失败")
                    self.show_message_safely("error", "错误", "集成学习失败")

            except Exception as e:
                error_msg = f"集成学习过程中出错: {str(e)}"
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n错误: {error_msg}\n")
                self.ensemble_progress.stop()
                self.ensemble_status_var.set("执行出错")
                self.show_message_safely("error", "错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_ensemble, daemon=True).start()

    def update_performance_comparison(self, performance_data):
        """更新性能对比显示"""
        self.update_gui_safely(self.performance_text, 'delete', '1.0', tk.END)

        if not performance_data:
            self.update_gui_safely(self.performance_text, 'insert', tk.END, "暂无性能数据\n")
            return

        # 表头
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "=" * 110 + "\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "集成学习性能对比表\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "=" * 110 + "\n\n")

        # 表格标题 - 增加新列
        header = f"{'模型名称':<15} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'AUC':<8} {'AUPRC':<8} {'Bal_Acc':<8} {'Brier':<8} {'MCC':<8}\n"
        self.update_gui_safely(self.performance_text, 'insert', tk.END, header)
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "-" * 110 + "\n")

        # 按F1分数排序
        sorted_data = sorted(performance_data, key=lambda x: x[1]['f1_score'], reverse=True)

        # 显示每个模型的性能 - 增加新列
        for i, (name, metrics) in enumerate(sorted_data):
            rank_symbol = "🏆" if i == 0 else f"{i+1:2d}"

            # 安全获取新增指标，如果不存在则显示"-"
            auc_pr = metrics.get('auc_pr', 0.0)
            balanced_acc = metrics.get('balanced_accuracy', 0.0)
            brier = metrics.get('brier_score', None)

            # 格式化Brier分数显示
            brier_str = f"{brier:<8.4f}" if brier is not None else f"{'N/A':<8}"

            row = f"{rank_symbol} {name:<12} {metrics['accuracy']:<8.4f} {metrics['precision']:<8.4f} " \
                  f"{metrics['recall']:<8.4f} {metrics['f1_score']:<8.4f} {metrics['auc']:<8.4f} " \
                  f"{auc_pr:<8.4f} {balanced_acc:<8.4f} {brier_str} {metrics.get('mcc', 0):<8.4f}\n"
            self.update_gui_safely(self.performance_text, 'insert', tk.END, row)

        # 统计信息
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "\n" + "=" * 110 + "\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, "统计信息:\n")

        f1_scores = [metrics['f1_score'] for _, metrics in performance_data]
        accuracies = [metrics['accuracy'] for _, metrics in performance_data]

        self.update_gui_safely(self.performance_text, 'insert', tk.END, f"• 平均F1分数: {sum(f1_scores)/len(f1_scores):.4f}\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, f"• 平均准确率: {sum(accuracies)/len(accuracies):.4f}\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, f"• 最佳F1分数: {max(f1_scores):.4f}\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, f"• 最佳准确率: {max(accuracies):.4f}\n")
        self.update_gui_safely(self.performance_text, 'insert', tk.END, f"• 性能提升: {((max(f1_scores) - min(f1_scores))/min(f1_scores)*100):.2f}%\n")

    def start_ensemble(self):
        self.ensemble_learning()

    def ensemble_visualization(self):
        """集成学习可视化 - 使用与命令行版本相同的SHAP分析"""
        if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
            messagebox.showwarning("Warning", "Please run ensemble learning first")
            return

        def run_enhanced_visualization():
            try:
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\nGenerating enhanced visualization with SHAP analysis...\n")

                # 使用与命令行版本相同的增强SHAP可视化
                try:
                    import sys
                    from pathlib import Path
                    sys.path.append(str(Path(__file__).parent / 'code'))

                    from safe_visualization import safe_ensemble_performance_plot, safe_create_summary_report

                    output_dir = OUTPUT_PATH / 'ensemble' / 'visualizations'
                    output_dir.mkdir(parents=True, exist_ok=True)

                    # 生成基础性能对比图（PDF格式）
                    plot_success = safe_ensemble_performance_plot(
                        self.current_ensemble_results,
                        output_dir / "ensemble_performance.pdf"
                    )

                    # 生成总结报告
                    report_success = safe_create_summary_report(
                        self.current_ensemble_results,
                        output_dir
                    )

                    if plot_success:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "✅ Performance chart generated successfully!\n")
                    if report_success:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "✅ Summary report generated successfully!\n")

                    # 为每个集成模型生成SHAP分析（与命令行版本一致）
                    if hasattr(self, 'current_data_path') and self.current_data_path.get():
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\nGenerating SHAP analysis for ensemble models...\n")

                        # 加载测试数据
                        import pandas as pd
                        from sklearn.model_selection import train_test_split

                        data = pd.read_csv(self.current_data_path.get())
                        # 优先使用明确的目标列名 'label'，避免将目标列混入特征
                        if 'label' in data.columns:
                            y = data['label']
                            X = data.drop(columns=['label'])
                        else:
                            # 兜底：保持原有逻辑（最后一列为目标变量）
                            X = data.iloc[:, :-1]  # 假设最后一列是目标变量，使用iloc避免类型问题
                            y = data.iloc[:, -1]

                        # 分割数据
                        _, X_test, _, _ = train_test_split(X, y, test_size=0.2, random_state=42)

                        shap_success_count = 0
                        if self.current_ensemble_results:
                            for name, result in self.current_ensemble_results.items():
                                if 'model' in result:
                                    try:
                                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  Generating SHAP for {name}...\n")

                                        # 创建模型专用输出目录
                                        model_output_dir = output_dir / 'shap_analysis' / name
                                        model_output_dir.mkdir(parents=True, exist_ok=True)

                                        # 使用与命令行版本完全相同的SHAP分析
                                        from gui_functions import GUIFunctions
                                        gui_func = GUIFunctions(self)
                                        shap_success = gui_func.create_command_line_shap_plots(
                                            model=result['model'],
                                            X_test=X_test,
                                            model_name=name,
                                            output_dir=model_output_dir
                                        )

                                        if shap_success:
                                            shap_success_count += 1
                                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"    ✅ {name} SHAP analysis completed\n")

                                            # 记录生成的文件
                                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "      Generated: summary, decision, waterfall plots (PDF format)\n")
                                        else:
                                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"    ⚠️ {name} SHAP analysis failed\n")

                                    except Exception as e:
                                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"    ❌ {name} SHAP analysis failed: {e}\n")

                        if shap_success_count > 0:
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n✅ SHAP analysis completed for {shap_success_count} models\n")
                        else:
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n⚠️ No SHAP analysis was generated\n")

                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\nAll charts saved to: {output_dir}\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "📄 All charts are saved in PDF format for high-quality editing\n")
                    self.show_message_safely("info", "Success", f"Enhanced visualization with SHAP analysis completed!\nCharts saved in PDF format to:\n{output_dir}\n\nPDF format preserves vector graphics for editing in design software.")

                except ImportError as e:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\nWarning: Enhanced SHAP visualization not available: {e}\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "Falling back to basic visualization...\n")

                    # 回退到基础可视化
                    sys.path.append(str(Path(__file__).parent / 'code'))
                    from safe_visualization import safe_ensemble_performance_plot, safe_create_summary_report

                    output_dir = OUTPUT_PATH / 'ensemble' / 'visualizations'
                    output_dir.mkdir(parents=True, exist_ok=True)

                    plot_success = safe_ensemble_performance_plot(
                        self.current_ensemble_results,
                        output_dir / "ensemble_performance.pdf"
                    )
                    report_success = safe_create_summary_report(
                        self.current_ensemble_results,
                        output_dir
                    )

                    if plot_success and report_success:
                        self.show_message_safely("info", "Success", f"Basic visualization completed!\nCharts saved in PDF format to:\n{output_dir}\n\nPDF format preserves vector graphics for editing in design software.")

            except Exception as e:
                error_msg = f"Visualization failed: {str(e)}"
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\nError: {error_msg}\n")
                self.show_message_safely("error", "Error", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_enhanced_visualization, daemon=True).start()

    def export_ensemble_results(self):
        """导出集成学习结果为CSV和JSON格式"""
        if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
            messagebox.showwarning("警告", "请先运行集成学习")
            return

        try:
            # 选择保存目录
            save_dir = filedialog.askdirectory(title="选择导出目录")
            if not save_dir:
                return

            save_path = Path(save_dir)
            # 记录最近一次导出目录，供“打开文件夹”使用
            try:
                self.last_export_dir = save_path
            except Exception:
                pass
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 准备导出数据
            export_data = []
            for name, result in self.current_ensemble_results.items():
                metrics = result['metrics']
                row = {
                    '模型名称': name,
                    '集成方法': result.get('method', 'unknown'),
                    '准确率': metrics['accuracy'],
                    '精确率': metrics['precision'],
                    '召回率': metrics['recall'],
                    'F1分数': metrics['f1_score'],
                    'AUC': metrics['auc'],
                    'AUPRC': metrics.get('auc_pr', 0.0),
                    '平衡准确率': metrics.get('balanced_accuracy', 0.0),
                    'Brier分数': metrics.get('brier_score', None),
                    'MCC': metrics.get('mcc', 0.0),
                    '投票类型': result.get('voting', 'N/A')
                }
                export_data.append(row)

            # 导出为CSV
            import pandas as pd
            df = pd.DataFrame(export_data)
            csv_file = save_path / f"ensemble_results_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')

            # 导出为JSON
            import json
            json_file = save_path / f"ensemble_results_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            messagebox.showinfo("成功", f"结果已导出到:\n{csv_file}\n{json_file}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def open_results_folder(self):
        """打开集成学习结果文件夹"""
        try:
            from config import OUTPUT_PATH
            # 优先打开最近一次“导出结果”的目录；否则回退到可视化目录
            results_dir = None
            try:
                if hasattr(self, 'last_export_dir') and self.last_export_dir:
                    results_dir = Path(self.last_export_dir)
            except Exception:
                results_dir = None
            if results_dir is None:
                results_dir = OUTPUT_PATH / 'ensemble' / 'visualizations'

            # 确保目录存在
            results_dir.mkdir(parents=True, exist_ok=True)

            # 根据操作系统打开文件夹
            import platform
            import subprocess

            system = platform.system()
            if system == "Windows":
                subprocess.run(['explorer', str(results_dir)], check=True)
            elif system == "Darwin":  # macOS
                subprocess.run(['open', str(results_dir)], check=True)
            else:  # Linux
                subprocess.run(['xdg-open', str(results_dir)], check=True)

        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def external_validation(self):
        """外部验证功能"""
        if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
            messagebox.showwarning("警告", "请先运行集成学习")
            return

        # 选择外部验证数据文件
        external_data_path = filedialog.askopenfilename(
            title="选择外部验证数据文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if not external_data_path:
            return

        def run_validation_process():
            try:
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n正在进行外部验证...\n")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"外部数据: {external_data_path}\n")

                # 运行外部验证 - 需要为每个模型单独运行
                validation_results = {}
                for model_name in MODEL_NAMES:
                    cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                    if cache_file.exists():
                        try:
                            result = run_external_validation(model_name, external_data_path)
                            validation_results[model_name] = result
                        except Exception as e:
                            self.logger.error(f"外部验证失败 {model_name}: {e}")
                            validation_results[model_name] = False

                if validation_results:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "外部验证完成！\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"验证结果保存到: {OUTPUT_PATH / 'external_validation'}\n")
                    self.show_message_safely("info", "成功", "外部验证完成！")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "外部验证失败！\n")
                    self.show_message_safely("error", "错误", "外部验证失败")

            except Exception as e:
                error_msg = f"外部验证过程中出错: {str(e)}"
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n错误: {error_msg}\n")
                self.show_message_safely("error", "错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_validation_process, daemon=True).start()

    def show_single_ensemble_info(self):
        """显示单数据源集成学习信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 单数据源集成学习 ===

使用说明:
1. 确保已选择数据文件
2. 选择至少2个要集成的模型
3. 选择集成方法（投票法、装袋法、提升法、堆叠法）
4. 点击"开始集成"按钮开始训练
5. 训练完成后可查看可视化结果

支持的集成方法:
• Voting (投票法): 硬投票和软投票
• Bagging (装袋法): 基于Bootstrap采样
• Boosting (提升法): AdaBoost算法
• Stacking (堆叠法): 使用元分类器

特点:
- 自动模型训练和集成
- 性能指标对比
- SHAP可解释性分析
- 可视化图表生成
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def show_multi_ensemble_info(self):
        """显示多数据源集成学习信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 多数据源集成学习 ===

使用说明:
1. 点击"生成配置"创建模型-数据源映射
2. 或选择已有的配置文件
3. 选择集成方法和数据策略
4. 配置特征选择参数
5. 点击"开始多数据源集成"

数据策略:
• Unified: 使用统一测试数据评估
• Original: 每个模型使用对应测试数据
• Combined: 合并所有数据源

特征选择方法:
• Combined: 组合特征重要性
• Union: 特征并集
• Intersection: 特征交集
• Weighted: 加权特征选择
• Meta_model: 元模型特征选择

注意事项:
- 不同数据源应具有相同的目标变量
- 特征名称可以不同，系统会自动处理
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def show_external_validation_info(self):
        """显示外部验证信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 外部验证 ===

使用说明:
1. 先完成集成学习训练
2. 点击"外部验证"按钮
3. 选择外部验证数据文件
4. 系统将使用训练好的模型进行验证

验证内容:
• 模型在外部数据上的性能
• 性能指标计算和对比
• 预测结果分析
• 可视化图表生成

数据要求:
- CSV格式文件
- 包含与训练数据相同的特征
- 可以有不同的样本数量
- 目标变量名称应一致

输出结果:
- 验证性能报告
- 预测结果文件
- 可视化图表
- 详细分析报告
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def start_multi_data_ensemble(self):
        """开始多数据源集成学习"""
        config_path = self.config_path_var.get()
        if not config_path:
            messagebox.showerror("错误", "请先选择或生成配置文件")
            return

        # 获取选中的集成方法
        selected_methods = [method for method, var in self.ensemble_methods.items() if var.get()]
        if not selected_methods:
            messagebox.showerror("错误", "请至少选择一种集成方法")
            return

        # 获取选中的数据策略
        selected_strategies = [strategy for strategy, var in self.data_strategies.items() if var.get()]
        if not selected_strategies:
            messagebox.showerror("错误", "请至少选择一种数据策略")
            return

        def run_multi_ensemble():
            try:
                self.update_gui_safely(self.ensemble_result_text, 'delete', '1.0', tk.END)
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "正在加载配置文件...\n")

                # 读取配置文件
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    model_data_mapping = config_data.get('model_data_mapping', {})

                if not model_data_mapping:
                    raise ValueError("配置文件中未找到有效的模型-数据映射")

                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"配置加载完成，包含 {len(model_data_mapping)} 个映射\n")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"集成方法: {', '.join(selected_methods)}\n")
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"数据策略: {', '.join(selected_strategies)}\n\n")

                # 运行多数据源集成学习
                results = run_multi_data_ensemble_pipeline(
                    model_data_mapping=model_data_mapping,
                    ensemble_methods=selected_methods,
                    ensemble_data_strategies=selected_strategies,
                    target_data_path=None,
                    feature_names=None,
                    enable_shap=True,
                    feature_selection=self.feature_selection_var.get(),
                    feature_selection_method=self.feature_method_var.get(),
                    k=self.feature_k_var.get() if self.feature_k_var.get() > 0 else None
                )

                if results:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "多数据源集成学习完成！\n\n")
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "=== 集成结果 ===\n")

                    # 显示结果
                    for strategy, method, _, metrics in results:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n{strategy}_{method}:\n")
                        if isinstance(metrics, dict):
                            for metric_name, value in metrics.items():
                                if isinstance(value, (int, float)):
                                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  {metric_name}: {value:.4f}\n")

                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n结果已保存到: {OUTPUT_PATH / 'ensemble'}\n")
                    self.show_message_safely("info", "成功", "多数据源集成学习完成！")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "多数据源集成学习失败！\n")
                    self.show_message_safely("error", "错误", "多数据源集成学习失败")

            except Exception as e:
                error_msg = f"多数据源集成学习过程中出错: {str(e)}"
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n错误: {error_msg}\n")
                self.show_message_safely("error", "错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_multi_ensemble, daemon=True).start()

    def smart_model_selection(self):
        """智能模型选择功能"""
        if not self.current_data_path.get():
            messagebox.showerror("错误", "请先选择数据文件")
            return

        def run_smart_selection():
            try:
                self.update_gui_safely(self.ensemble_result_text, 'delete', '1.0', tk.END)
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "正在进行智能模型选择分析...\n")

                # 导入增强的集成选择器
                sys.path.append(str(Path(__file__).parent / 'code'))
                from enhanced_ensemble_selector import EnhancedEnsembleSelector
                from data_preprocessing import load_and_preprocess_data

                # 加载数据
                X_train, X_test, y_train, y_test = load_and_preprocess_data(self.current_data_path.get())

                # 获取当前使用的scaler，传递给模型训练器
                from data_preprocessing import get_current_scaler
                current_scaler = get_current_scaler()

                if current_scaler is not None:
                    # 为所有模型设置scaler
                    for model_name in MODEL_TRAINERS.keys():
                        setattr(MODEL_TRAINERS[model_name], '_scaler', current_scaler)

                # 创建选择器
                selector = EnhancedEnsembleSelector(
                    correlation_threshold=self.correlation_threshold_var.get(),
                    min_performance_threshold=self.min_performance_var.get()
                )

                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "正在评估基模型性能...\n")

                # 评估所有可用模型
                available_models = list(MODEL_TRAINERS.keys())
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"开始评估 {len(available_models)} 个模型...\n")
                
                model_results = selector.evaluate_base_models(
                    X_train, y_train, X_test, y_test, available_models
                )

                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n✅ 成功评估 {len(model_results)} 个模型\n")
                if len(model_results) < len(available_models):
                    failed_models = set(available_models) - set(model_results.keys())
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"⚠️ 以下模型评估失败: {', '.join(failed_models)}\n")

                # 选择最优组合
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n正在选择最优模型组合...\n")
                
                selection_result = selector.select_optimal_ensemble(
                    target_size=self.target_size_var.get(),
                    strategy=self.selection_strategy_var.get()
                )

                if selection_result:
                    # 显示选择结果
                    report = selector.generate_selection_report(selection_result)
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n" + report + "\n")

                    # 如果使用了量化评估，显示量化多样性报告
                    if (self.selection_strategy_var.get() == 'quantified' and
                        hasattr(selector, 'quantified_diversity_results') and
                        selector.quantified_diversity_results):

                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n" + "="*60 + "\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "量化多样性分析报告\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "="*60 + "\n")

                        try:
                            # 获取预测结果
                            predictions = {name: selector.model_results[name]['predictions']
                                         for name in selector.model_results.keys()}

                            # 生成量化多样性报告
                            quantified_report = selector.quantified_evaluator.generate_comprehensive_diversity_report(
                                y_test, predictions, selector.performance_scores
                            )

                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, quantified_report + "\n")
                        except Exception as e:
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n⚠️ 量化多样性报告生成失败: {str(e)}\n")
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "但模型选择仍然完成，使用了传统的评估方法\n")
                    elif self.selection_strategy_var.get() == 'quantified':
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n⚠️ 量化评估未能完成，已回退到传统评估方法\n")

                    # 自动更新模型选择
                    selected_models = selection_result['selected_models']

                    # 清除当前选择
                    for model, var in self.model_vars.items():
                        var.set(False)

                    # 设置推荐的模型
                    for model in selected_models:
                        if model in self.model_vars:
                            self.model_vars[model].set(True)

                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n✅ 已自动选择推荐模型: {', '.join(selected_models)}\n")

                    # 计算最优权重
                    optimal_weights = selector.calculate_optimal_weights(
                        selected_models, method='performance_diversity'
                    )

                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n推荐融合权重:\n")
                    for model, weight in optimal_weights.items():
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  {model}: {weight:.4f}\n")

                    # 保存选择结果供后续使用
                    self.current_selection_result = selection_result
                    self.current_optimal_weights = optimal_weights

                    # 自动生成可视化文件
                    try:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n📊 正在生成可视化文件...\n")
                        self.root.update()
                        
                        # 导入可视化模块
                        from ensemble_visualization import EnsembleVisualizer
                        
                        # 创建可视化器
                        visualizer = EnsembleVisualizer()
                        
                        # 生成所有可视化文件
                        generated_files = visualizer.generate_all_visualizations(selector, selection_result)
                        
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n✅ 可视化文件生成完成！\n")
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "生成的文件:\n")
                        for file_type, file_path in generated_files.items():
                            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"  - {file_type}: {file_path}\n")
                        
                        # 保存生成的文件路径供后续使用
                        self.current_visualization_files = generated_files
                        
                    except Exception as viz_error:
                        self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n⚠️ 可视化文件生成失败: {str(viz_error)}\n")

                    self.show_message_safely("info", "成功", "智能模型选择完成！已自动选择推荐的模型组合。")
                else:
                    self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "\n❌ 智能模型选择失败\n")
                    self.show_message_safely("error", "错误", "智能模型选择失败")

            except Exception as e:
                error_msg = f"智能模型选择过程中出错: {str(e)}"
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"\n错误: {error_msg}\n")
                self.show_message_safely("error", "错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_smart_selection, daemon=True).start()

    def view_ensemble_results(self):
        """查看集成学习结果"""
        ensemble_dir = OUTPUT_PATH / 'ensemble'
        if not ensemble_dir.exists():
            messagebox.showinfo("提示", "尚未运行集成学习，没有结果可查看")
            return

        # 查找结果文件
        result_files = list(ensemble_dir.glob('*results*.joblib'))
        if not result_files:
            messagebox.showinfo("提示", "未找到集成学习结果文件")
            return

        # 显示最新的结果文件
        latest_file = max(result_files, key=lambda x: x.stat().st_mtime)

        self.ensemble_result_text.delete(1.0, tk.END)
        self.ensemble_result_text.insert(tk.END, "=== 集成学习结果 ===\n")
        self.ensemble_result_text.insert(tk.END, f"结果文件: {latest_file.name}\n")
        self.ensemble_result_text.insert(tk.END, f"修改时间: {datetime.fromtimestamp(latest_file.stat().st_mtime)}\n\n")

        try:
            from joblib import load
            results = load(latest_file)

            if isinstance(results, dict) and 'ensemble_results' in results:
                ensemble_results = results['ensemble_results']
                self.ensemble_result_text.insert(tk.END, "=== 性能指标 ===\n")

                for name, result in ensemble_results.items():
                    if 'metrics' in result:
                        metrics = result['metrics']
                        self.ensemble_result_text.insert(tk.END, f"\n{name}:\n")
                        for metric_name, value in metrics.items():
                            if isinstance(value, (int, float)):
                                self.ensemble_result_text.insert(tk.END, f"  {metric_name}: {value:.4f}\n")

                if 'best_model' in results:
                    best_model = results['best_model']
                    self.ensemble_result_text.insert(tk.END, f"\n🏆 最佳模型: {best_model}\n")

        except Exception as e:
            self.ensemble_result_text.insert(tk.END, f"读取结果文件失败: {str(e)}\n")

    def open_output_directory(self):
        """打开输出目录"""
        import subprocess
        import platform

        output_dir = OUTPUT_PATH

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(output_dir)], check=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(output_dir)], check=True)
            else:  # Linux
                subprocess.run(["xdg-open", str(output_dir)], check=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开输出目录: {str(e)}")
            # 备选方案：显示路径
            messagebox.showinfo("输出目录", f"输出目录路径:\n{output_dir}")
    def generate_report(self):
        """生成模型性能比较报告"""
        if hasattr(self, 'functions'):
            self.functions.generate_performance_report()
        else:
            messagebox.showerror("错误", "GUI功能模块未初始化")

    def generate_performance_report(self):
        """生成性能报告的包装函数"""
        self.generate_report()
    def browse_config_file(self):
        messagebox.showinfo("提示", "配置文件浏览功能开发中...")

    def generate_config_placeholder(self):
        messagebox.showinfo("提示", "配置生成功能开发中...")

    def toggle_log_panel(self):
        messagebox.showinfo("提示", "日志面板切换功能开发中...")

    def toggle_fullscreen(self):
        messagebox.showinfo("提示", "全屏切换功能开发中...")
    def data_quality_check(self):
        """数据质量检查功能"""
        if not self.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        try:
            # 创建数据质量检查窗口
            quality_window = tk.Toplevel(self.root)
            quality_window.title("数据质量检查")
            quality_window.geometry("800x600")
            quality_window.transient(self.root)
            quality_window.grab_set()

            # 创建进度显示
            progress_frame = ttk.Frame(quality_window)
            progress_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Label(progress_frame, text="正在进行数据质量检查...").pack(side=tk.LEFT)
            progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
            progress_bar.pack(side=tk.RIGHT, padx=10)
            progress_bar.start()

            # 结果显示区域
            result_frame = ttk.LabelFrame(quality_window, text="数据质量检查结果")
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            result_text = tk.Text(result_frame, wrap=tk.WORD, font=('Consolas', 10))
            result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_text.yview)
            result_text.configure(yscrollcommand=result_scrollbar.set)

            result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 在后台线程中执行数据质量检查
            def run_quality_check():
                try:
                    from code.data_validation_eda import run_complete_data_analysis
                    from code.data_preprocessing import load_and_clean_data

                    # 加载数据
                    df, target_col = load_and_clean_data(self.current_data_path.get())

                    # 检查返回值是否有效
                    if df is None:
                        raise ValueError("数据加载失败，返回空值")

                    # 执行数据质量分析
                    results = run_complete_data_analysis(self.current_data_path.get(), target_col)

                    # 在主线程中更新UI
                    def update_ui():
                        progress_bar.stop()
                        progress_frame.destroy()

                        # 格式化结果显示
                        report_text = self._format_quality_report(results, df, target_col)
                        result_text.insert(tk.END, report_text)

                        # 添加操作按钮
                        button_frame = ttk.Frame(quality_window)
                        button_frame.pack(fill=tk.X, padx=10, pady=5)

                        ttk.Button(button_frame, text="保存报告",
                                 command=lambda: self._save_quality_report(report_text)).pack(side=tk.LEFT, padx=5)
                        ttk.Button(button_frame, text="关闭",
                                 command=quality_window.destroy).pack(side=tk.RIGHT, padx=5)

                    quality_window.after(0, update_ui)

                except Exception as e:
                    error_msg = str(e)
                    def show_error():
                        progress_bar.stop()
                        self.show_message_safely("error", "错误", f"数据质量检查失败: {error_msg}")
                        quality_window.destroy()

                    quality_window.after(0, show_error)

            # 启动后台线程
            threading.Thread(target=run_quality_check, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动数据质量检查失败: {str(e)}")

    def model_selection_advisor(self):
        """模型选择建议功能"""
        if not self.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        try:
            # 创建模型选择建议窗口
            advisor_window = tk.Toplevel(self.root)
            advisor_window.title("模型选择建议")
            advisor_window.geometry("900x700")
            advisor_window.transient(self.root)
            advisor_window.grab_set()

            # 配置参数区域
            config_frame = ttk.LabelFrame(advisor_window, text="分析配置")
            config_frame.pack(fill=tk.X, padx=10, pady=5)

            # 第一行配置
            row1_frame = ttk.Frame(config_frame)
            row1_frame.pack(fill=tk.X, padx=5, pady=5)

            ttk.Label(row1_frame, text="目标模型数量:").pack(side=tk.LEFT, padx=5)
            target_size_var = tk.IntVar(value=3)
            ttk.Spinbox(row1_frame, from_=2, to=8, textvariable=target_size_var, width=5).pack(side=tk.LEFT, padx=5)

            ttk.Label(row1_frame, text="选择策略:").pack(side=tk.LEFT, padx=(20, 5))
            strategy_var = tk.StringVar(value="balanced")
            strategy_combo = ttk.Combobox(row1_frame, textvariable=strategy_var,
                                        values=["performance", "diversity", "balanced", "quantified"],
                                        state="readonly", width=12)
            strategy_combo.pack(side=tk.LEFT, padx=5)

            # 第二行配置
            row2_frame = ttk.Frame(config_frame)
            row2_frame.pack(fill=tk.X, padx=5, pady=5)

            ttk.Label(row2_frame, text="最低性能阈值:").pack(side=tk.LEFT, padx=5)
            min_perf_var = tk.DoubleVar(value=0.6)
            ttk.Entry(row2_frame, textvariable=min_perf_var, width=8).pack(side=tk.LEFT, padx=5)

            ttk.Label(row2_frame, text="相关性阈值:").pack(side=tk.LEFT, padx=(20, 5))
            corr_threshold_var = tk.DoubleVar(value=0.3)
            ttk.Entry(row2_frame, textvariable=corr_threshold_var, width=8).pack(side=tk.LEFT, padx=5)

            # 开始分析按钮
            ttk.Button(config_frame, text="🚀 开始分析",
                     command=lambda: self._run_model_advisor(advisor_window, target_size_var.get(),
                                                           strategy_var.get(), min_perf_var.get(),
                                                           corr_threshold_var.get())).pack(pady=10)

            # 结果显示区域
            result_frame = ttk.LabelFrame(advisor_window, text="分析结果")
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            # 创建选项卡
            result_notebook = ttk.Notebook(result_frame)
            result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 推荐结果选项卡
            recommendation_tab = ttk.Frame(result_notebook)
            result_notebook.add(recommendation_tab, text="推荐结果")

            self.advisor_result_text = tk.Text(recommendation_tab, wrap=tk.WORD, font=('Consolas', 10))
            advisor_scrollbar = ttk.Scrollbar(recommendation_tab, orient=tk.VERTICAL, command=self.advisor_result_text.yview)
            self.advisor_result_text.configure(yscrollcommand=advisor_scrollbar.set)

            self.advisor_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            advisor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 详细分析选项卡
            detail_tab = ttk.Frame(result_notebook)
            result_notebook.add(detail_tab, text="详细分析")

            self.advisor_detail_text = tk.Text(detail_tab, wrap=tk.WORD, font=('Consolas', 9))
            detail_scrollbar = ttk.Scrollbar(detail_tab, orient=tk.VERTICAL, command=self.advisor_detail_text.yview)
            self.advisor_detail_text.configure(yscrollcommand=detail_scrollbar.set)

            self.advisor_detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            messagebox.showerror("错误", f"启动模型选择建议失败: {str(e)}")

    def config_generator(self):
        """配置生成器功能"""
        try:
            # 创建配置生成器窗口
            config_window = tk.Toplevel(self.root)
            config_window.title("配置生成器")
            config_window.geometry("700x500")
            config_window.transient(self.root)
            config_window.grab_set()

            # 配置类型选择
            type_frame = ttk.LabelFrame(config_window, text="配置类型")
            type_frame.pack(fill=tk.X, padx=10, pady=5)

            config_type_var = tk.StringVar(value="multi_data")
            ttk.Radiobutton(type_frame, text="多数据源集成配置", variable=config_type_var,
                          value="multi_data").pack(anchor=tk.W, padx=10, pady=2)
            ttk.Radiobutton(type_frame, text="训练参数配置", variable=config_type_var,
                          value="training").pack(anchor=tk.W, padx=10, pady=2)
            ttk.Radiobutton(type_frame, text="超参数调优配置", variable=config_type_var,
                          value="hyperparameter").pack(anchor=tk.W, padx=10, pady=2)

            # 配置内容区域
            content_frame = ttk.LabelFrame(config_window, text="配置内容")
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            # 创建选项卡
            config_notebook = ttk.Notebook(content_frame)
            config_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 多数据源配置选项卡
            multi_data_tab = ttk.Frame(config_notebook)
            config_notebook.add(multi_data_tab, text="多数据源配置")

            # 模型-数据映射表格
            ttk.Label(multi_data_tab, text="模型-数据源映射:").pack(anchor=tk.W, padx=5, pady=5)

            columns = ("模型", "数据文件路径")
            mapping_tree = ttk.Treeview(multi_data_tab, columns=columns, show="headings", height=8)

            for col in columns:
                mapping_tree.heading(col, text=col)
                mapping_tree.column(col, width=300)

            mapping_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 映射操作按钮
            mapping_button_frame = ttk.Frame(multi_data_tab)
            mapping_button_frame.pack(fill=tk.X, padx=5, pady=5)

            ttk.Button(mapping_button_frame, text="添加映射",
                     command=lambda: self._add_model_data_mapping(mapping_tree)).pack(side=tk.LEFT, padx=5)
            ttk.Button(mapping_button_frame, text="删除映射",
                     command=lambda: self._remove_model_data_mapping(mapping_tree)).pack(side=tk.LEFT, padx=5)

            # 训练参数配置选项卡
            training_tab = ttk.Frame(config_notebook)
            config_notebook.add(training_tab, text="训练参数配置")

            self._create_training_config_tab(training_tab)

            # 超参数调优配置选项卡
            hyperparameter_tab = ttk.Frame(config_notebook)
            config_notebook.add(hyperparameter_tab, text="超参数调优配置")

            self._create_hyperparameter_config_tab(hyperparameter_tab)

            # 操作按钮
            button_frame = ttk.Frame(config_window)
            button_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Button(button_frame, text="生成配置",
                     command=lambda: self._generate_config_file(config_type_var.get(), mapping_tree, config_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="预览配置",
                     command=lambda: self._preview_config(config_type_var.get(), mapping_tree)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭",
                     command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"启动配置生成器失败: {str(e)}")

    def _format_quality_report(self, results, df, target_col):
        """格式化数据质量报告"""
        report = "=" * 60 + "\n"
        report += "数据质量检查报告\n"
        report += "=" * 60 + "\n\n"

        # 基本信息
        report += "数据集基本信息:\n"
        report += f"  - 数据形状: {df.shape[0]} 行 × {df.shape[1]} 列\n"
        report += f"  - 目标列: {target_col}\n"
        report += f"  - 内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\n\n"

        # 数据完整性
        if 'data_validation' in results:
            validation = results['data_validation']
            report += "数据完整性检查:\n"

            # 缺失值
            missing_info = validation.get('integrity', {}).get('missing_values', {})
            total_missing = missing_info.get('total_missing', 0)
            report += f"  - 缺失值总数: {total_missing}\n"
            if total_missing > 0:
                report += f"  - 缺失值比例: {total_missing / (df.shape[0] * df.shape[1]) * 100:.2f}%\n"

            # 重复值
            duplicates = validation.get('integrity', {}).get('duplicates', {})
            duplicate_rows = duplicates.get('duplicate_rows', 0)
            report += f"  - 重复行数: {duplicate_rows}\n"

            # 数据类型
            report += f"  - 数值型列数: {len(df.select_dtypes(include=['number']).columns)}\n"
            report += f"  - 分类型列数: {len(df.select_dtypes(include=['object']).columns)}\n\n"

        # 异常值检测
        if 'outlier_detection' in results:
            outliers = results['outlier_detection']
            total_outliers = 0
            for col_results in outliers.values():
                for method_results in col_results.values():
                    total_outliers += method_results.get('outlier_count', 0)

            report += "异常值检测:\n"
            report += f"  - 检测到异常值总数: {total_outliers}\n"
            if total_outliers > 0:
                report += f"  - 异常值比例: {total_outliers / df.shape[0] * 100:.2f}%\n"
            report += "\n"

        # 数据质量建议
        if 'recommendations' in results:
            recommendations = results['recommendations']
            if recommendations:
                report += "数据质量改进建议:\n"
                for i, rec in enumerate(recommendations, 1):
                    report += f"  {i}. {rec}\n"
                report += "\n"

        # 目标变量分布
        if target_col in df.columns:
            target_counts = df[target_col].value_counts()
            report += "目标变量分布:\n"
            for value, count in target_counts.items():
                percentage = count / len(df) * 100
                report += f"  - {value}: {count} ({percentage:.1f}%)\n"
            report += "\n"

        # 特征统计摘要
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            report += "数值特征统计摘要:\n"
            stats = df[numeric_cols].describe()
            for col in numeric_cols[:5]:  # 只显示前5个特征
                report += f"  {col}:\n"
                report += f"    均值: {stats.loc['mean', col]:.3f}, "
                report += f"标准差: {stats.loc['std', col]:.3f}\n"
                report += f"    最小值: {stats.loc['min', col]:.3f}, "
                report += f"最大值: {stats.loc['max', col]:.3f}\n"
            if len(numeric_cols) > 5:
                report += f"  ... 还有 {len(numeric_cols) - 5} 个数值特征\n"
            report += "\n"

        report += "=" * 60 + "\n"
        report += "报告生成完成\n"
        report += "=" * 60

        return report

    def _save_quality_report(self, report_text):
        """保存数据质量报告"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存数据质量报告",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                messagebox.showinfo("成功", f"报告已保存到: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存报告失败: {str(e)}")

    def _run_model_advisor(self, window, target_size, strategy, min_perf, corr_threshold):
        """运行模型选择建议分析"""
        try:
            # 清空结果显示
            self.advisor_result_text.delete(1.0, tk.END)
            self.advisor_detail_text.delete(1.0, tk.END)

            self.update_gui_safely(self.advisor_result_text, 'insert', tk.END, "正在进行模型选择分析...\n")
            window.update()

            def run_analysis():
                try:
                    from enhanced_ensemble_selector import EnhancedEnsembleSelector
                    from data_preprocessing import load_and_preprocess_data

                    # 加载数据
                    X_train, X_test, y_train, y_test = load_and_preprocess_data(self.current_data_path.get())

                    # 获取当前使用的scaler，传递给模型训练器
                    from data_preprocessing import get_current_scaler
                    current_scaler = get_current_scaler()

                    if current_scaler is not None:
                        # 为所有模型设置scaler
                        for model_name in MODEL_TRAINERS.keys():
                            setattr(MODEL_TRAINERS[model_name], '_scaler', current_scaler)

                    # 创建选择器
                    selector = EnhancedEnsembleSelector(
                        correlation_threshold=corr_threshold,
                        min_performance_threshold=min_perf
                    )

                    # 评估基模型
                    selector.evaluate_base_models(X_train, X_test, y_train, y_test)

                    # 选择最优组合
                    result = selector.select_optimal_ensemble(target_size, strategy)

                    # 在主线程中更新UI
                    def update_ui():
                        self.advisor_result_text.delete(1.0, tk.END)
                        self.advisor_detail_text.delete(1.0, tk.END)

                        if result:
                            # 推荐结果
                            recommendation_text = self._format_model_recommendation(result, selector)
                            self.advisor_result_text.insert(tk.END, recommendation_text)

                            # 详细分析
                            detail_text = self._format_model_analysis_detail(selector)
                            self.advisor_detail_text.insert(tk.END, detail_text)
                        else:
                            self.advisor_result_text.insert(tk.END, "未能生成有效的模型推荐结果")

                    window.after(0, update_ui)

                except Exception as e:
                    error_msg = str(e)
                    def show_error():
                        self.advisor_result_text.delete(1.0, tk.END)
                        self.advisor_result_text.insert(tk.END, f"分析失败: {error_msg}")

                    window.after(0, show_error)

            # 启动后台线程
            threading.Thread(target=run_analysis, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动模型分析失败: {str(e)}")

    def _format_model_recommendation(self, result, selector):
        """格式化模型推荐结果"""
        text = "=" * 50 + "\n"
        text += "模型选择建议报告\n"
        text += "=" * 50 + "\n\n"

        # 推荐的模型组合
        text += "🎯 推荐的模型组合:\n"
        for i, model in enumerate(result['selected_models'], 1):
            performance = selector.performance_scores.get(model, 0)
            text += f"  {i}. {model} (性能得分: {performance:.4f})\n"

        text += f"\n📊 组合评分: {result['combination_score']:.4f}\n"
        text += f"🎯 选择策略: {result['strategy']}\n\n"

        # 权重分配
        if 'optimal_weights' in result:
            text += "⚖️ 建议权重分配:\n"
            for model, weight in result['optimal_weights'].items():
                text += f"  {model}: {weight:.3f}\n"
            text += "\n"

        # 性能预期
        text += "📈 预期性能提升:\n"
        text += f"  - 相比最佳单模型预期提升: {result.get('expected_improvement', 0):.2f}%\n"
        text += f"  - 多样性得分: {result.get('diversity_score', 0):.4f}\n\n"

        # 使用建议
        text += "💡 使用建议:\n"
        text += "  1. 使用推荐的模型组合进行集成学习\n"
        text += "  2. 采用建议的权重分配方案\n"
        text += "  3. 可以尝试不同的集成方法(投票、堆叠等)\n"
        text += "  4. 建议进行交叉验证以验证性能\n\n"

        text += "=" * 50

        return text

    def _format_model_analysis_detail(self, selector):
        """格式化详细分析结果"""
        text = "=" * 60 + "\n"
        text += "详细模型分析报告\n"
        text += "=" * 60 + "\n\n"

        # 所有模型性能
        text += "📊 所有模型性能评估:\n"
        text += "-" * 40 + "\n"

        for model_name, score in sorted(selector.performance_scores.items(),
                                      key=lambda x: x[1], reverse=True):
            if model_name in selector.model_results:
                metrics = selector.model_results[model_name]['metrics']
                text += f"{model_name}:\n"
                text += f"  综合得分: {score:.4f}\n"
                text += f"  准确率: {metrics.get('accuracy', 0):.4f}\n"
                text += f"  F1得分: {metrics.get('f1_score', 0):.4f}\n"
                text += f"  AUC: {metrics.get('auc_roc', 0):.4f}\n"
                text += f"  精确率: {metrics.get('precision', 0):.4f}\n"
                text += f"  召回率: {metrics.get('recall', 0):.4f}\n\n"

        # 模型多样性分析
        if hasattr(selector, 'diversity_matrix'):
            text += "🔄 模型多样性分析:\n"
            text += "-" * 40 + "\n"

            models = list(selector.performance_scores.keys())
            text += "相关性矩阵 (值越小表示多样性越高):\n"

            # 表头
            text += "模型".ljust(12)
            for model in models[:5]:  # 只显示前5个
                text += model[:8].ljust(10)
            text += "\n"

            # 矩阵内容
            for i, model1 in enumerate(models[:5]):
                text += model1[:10].ljust(12)
                for j, _ in enumerate(models[:5]):
                    if i < len(selector.diversity_matrix) and j < len(selector.diversity_matrix[i]):
                        corr = selector.diversity_matrix[i][j]
                        text += f"{corr:.3f}".ljust(10)
                    else:
                        text += "N/A".ljust(10)
                text += "\n"
            text += "\n"

        # 特征重要性分析（如果可用）
        text += "🎯 模型特点分析:\n"
        text += "-" * 40 + "\n"

        model_characteristics = {
            'RandomForest': '随机森林 - 适合处理非线性关系，特征重要性明确',
            'XGBoost': 'XGBoost - 梯度提升，处理复杂模式能力强',
            'LightGBM': 'LightGBM - 轻量级梯度提升，训练速度快',
            'Logistic': '逻辑回归 - 线性模型，可解释性强',
            'SVM': '支持向量机 - 适合高维数据，核技巧处理非线性',
            'KNN': 'K近邻 - 基于实例的学习，适合局部模式',
            'NaiveBayes': '朴素贝叶斯 - 概率模型，适合文本分类',
            'NeuralNet': '神经网络 - 深度学习，适合复杂非线性关系'
        }

        for model_name in selector.performance_scores.keys():
            if model_name in model_characteristics:
                text += f"{model_name}: {model_characteristics[model_name]}\n"

        text += "\n" + "=" * 60

        return text

    def _add_model_data_mapping(self, tree):
        """添加模型-数据映射（增强版）"""
        try:
            # 导入配置管理器
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'code'))
            from data_source_config import DataSourceModelConfig

            # 创建添加映射对话框
            add_window = tk.Toplevel(self.root)
            add_window.title("添加模型-数据映射")
            add_window.geometry("700x500")
            add_window.transient(self.root)
            add_window.grab_set()

            # 创建配置管理器
            config_manager = DataSourceModelConfig()

            # 模型选择
            ttk.Label(add_window, text="选择模型:").grid(row=0, column=0, padx=10, pady=10, sticky=tk.W)
            model_var = tk.StringVar()
            model_combo = ttk.Combobox(add_window, textvariable=model_var,
                                     values=MODEL_NAMES, state="readonly", width=20)
            model_combo.grid(row=0, column=1, padx=10, pady=10, sticky=tk.EW)

            # 数据文件选择
            ttk.Label(add_window, text="数据文件:").grid(row=1, column=0, padx=10, pady=10, sticky=tk.W)
            data_var = tk.StringVar()
            data_entry = ttk.Entry(add_window, textvariable=data_var, width=40)
            data_entry.grid(row=1, column=1, padx=10, pady=10, sticky=tk.EW)

            # 路径验证状态标签
            status_var = tk.StringVar(value="请选择数据文件")
            status_label = ttk.Label(add_window, textvariable=status_var, foreground="gray")
            status_label.grid(row=2, column=1, padx=10, pady=5, sticky=tk.W)

            def browse_data():
                file_path = filedialog.askopenfilename(
                    title="选择数据文件",
                    filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
                if file_path:
                    data_var.set(file_path)
                    validate_path()

            def validate_path():
                """验证数据路径"""
                path = data_var.get()
                if not path:
                    status_var.set("请选择数据文件")
                    status_label.config(foreground="gray")
                    return False

                is_valid = config_manager.validate_data_path(path)
                if is_valid:
                    status_var.set("✓ 数据文件有效")
                    status_label.config(foreground="green")

                    # 显示数据预览
                    show_data_preview(path)

                    # 显示推荐模型
                    show_recommended_models(path)
                else:
                    status_var.set("✗ 数据文件无效或不存在")
                    status_label.config(foreground="red")
                    preview_text.delete(1.0, tk.END)
                    recommend_text.delete(1.0, tk.END)

                return is_valid

            ttk.Button(add_window, text="浏览...", command=browse_data).grid(row=1, column=2, padx=10, pady=10)

            # 数据预览区域
            ttk.Label(add_window, text="数据预览:").grid(row=3, column=0, padx=10, pady=10, sticky=tk.NW)
            preview_frame = ttk.Frame(add_window)
            preview_frame.grid(row=3, column=1, columnspan=2, padx=10, pady=10, sticky=tk.EW+tk.NS)

            preview_text = tk.Text(preview_frame, height=8, width=60)
            preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=preview_text.yview)
            preview_text.configure(yscrollcommand=preview_scrollbar.set)
            preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 推荐模型区域
            ttk.Label(add_window, text="推荐模型:").grid(row=4, column=0, padx=10, pady=10, sticky=tk.NW)
            recommend_frame = ttk.Frame(add_window)
            recommend_frame.grid(row=4, column=1, columnspan=2, padx=10, pady=10, sticky=tk.EW)

            recommend_text = tk.Text(recommend_frame, height=3, width=60)
            recommend_text.pack(fill=tk.BOTH, expand=True)

            def show_data_preview(path):
                """显示数据预览"""
                try:
                    # 使用配置管理器获取数据信息
                    config_manager.add_mapping("temp", path)
                    preview_df = config_manager.get_data_preview("temp", n_rows=5)
                    data_info = config_manager.get_data_info("temp")
                    config_manager.remove_mapping("temp")

                    preview_text.delete(1.0, tk.END)

                    if preview_df is not None and data_info is not None:
                        # 显示基本信息
                        info_text = f"数据集信息:\n"
                        info_text += f"- 样本数: {data_info['n_samples']}\n"
                        info_text += f"- 特征数: {data_info['n_features']}\n"
                        info_text += f"- 数值特征: {data_info['numeric_features']}\n"
                        info_text += f"- 分类特征: {data_info['categorical_features']}\n"
                        info_text += f"- 文件大小: {data_info['file_size_mb']:.2f} MB\n\n"

                        # 显示数据预览
                        info_text += "数据预览:\n"
                        info_text += preview_df.to_string(index=False)

                        preview_text.insert(tk.END, info_text)
                    else:
                        preview_text.insert(tk.END, "无法预览数据")

                except Exception as e:
                    preview_text.delete(1.0, tk.END)
                    preview_text.insert(tk.END, f"预览失败: {str(e)}")

            def show_recommended_models(path):
                """显示推荐模型"""
                try:
                    recommended = config_manager.get_optimal_models_for_data(path)
                    recommend_text.delete(1.0, tk.END)
                    recommend_text.insert(tk.END, f"推荐模型: {', '.join(recommended)}")
                except Exception as e:
                    recommend_text.delete(1.0, tk.END)
                    recommend_text.insert(tk.END, f"推荐失败: {str(e)}")

            # 绑定路径变化事件
            data_var.trace('w', lambda *args: validate_path())

            # 确认按钮
            def confirm_add():
                if model_var.get() and data_var.get():
                    # 验证路径
                    if not validate_path():
                        messagebox.showerror("错误", "数据文件无效，请重新选择")
                        return

                    # 检查模型是否已存在
                    existing_models = []
                    for item in tree.get_children():
                        values = tree.item(item)['values']
                        existing_models.append(values[0])

                    if model_var.get() in existing_models:
                        messagebox.showwarning("警告", f"模型 {model_var.get()} 已存在映射")
                        return

                    tree.insert("", "end", values=(model_var.get(), data_var.get()))
                    add_window.destroy()
                else:
                    messagebox.showerror("错误", "请填写完整信息")

            button_frame = ttk.Frame(add_window)
            button_frame.grid(row=5, column=0, columnspan=3, pady=20)

            ttk.Button(button_frame, text="确定", command=confirm_add).pack(side=tk.LEFT, padx=10)
            ttk.Button(button_frame, text="取消", command=add_window.destroy).pack(side=tk.LEFT, padx=10)

            # 配置网格权重
            add_window.columnconfigure(1, weight=1)
            add_window.rowconfigure(3, weight=1)

        except Exception as e:
            messagebox.showerror("错误", f"添加映射失败: {str(e)}")

    def _remove_model_data_mapping(self, tree):
        """删除选中的模型-数据映射"""
        selected = tree.selection()
        if selected:
            for item in selected:
                tree.delete(item)
        else:
            messagebox.showwarning("警告", "请先选择要删除的映射")

    def _create_training_config_tab(self, parent):
        """创建训练参数配置选项卡"""
        # 基本训练参数
        basic_frame = ttk.LabelFrame(parent, text="基本参数")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # 测试集比例
        ttk.Label(basic_frame, text="测试集比例:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.training_test_size_var = tk.DoubleVar(value=0.2)
        # 绑定变化事件，与主训练参数同步
        self.training_test_size_var.trace_add('write', self._sync_test_size_from_training_config)
        ttk.Entry(basic_frame, textvariable=self.training_test_size_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        # 随机种子
        ttk.Label(basic_frame, text="随机种子:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.config_random_seed_var = tk.IntVar(value=42)
        ttk.Entry(basic_frame, textvariable=self.config_random_seed_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 特征缩放
        ttk.Label(basic_frame, text="特征缩放:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.config_scaling_var = tk.StringVar(value="standard")
        scaling_combo = ttk.Combobox(basic_frame, textvariable=self.config_scaling_var,
                                   values=["standard", "minmax", "robust", "none"], state="readonly", width=12)
        scaling_combo.grid(row=1, column=1, padx=5, pady=5)

        # 交叉验证折数
        ttk.Label(basic_frame, text="CV折数:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.config_cv_folds_var = tk.IntVar(value=5)
        ttk.Entry(basic_frame, textvariable=self.config_cv_folds_var, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 严格复现模式参数
        repro_frame = ttk.LabelFrame(parent, text="严格复现模式")
        repro_frame.pack(fill=tk.X, padx=5, pady=5)

        # 严格复现模式开关
        ttk.Label(repro_frame, text="严格复现模式:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.config_strict_repro_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(repro_frame, variable=self.config_strict_repro_var).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # 线程数设置
        ttk.Label(repro_frame, text="最大线程数:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.config_num_threads_var = tk.IntVar(value=1)
        ttk.Entry(repro_frame, textvariable=self.config_num_threads_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # CPU强制使用
        ttk.Label(repro_frame, text="强制使用CPU:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.config_enforce_cpu_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(repro_frame, variable=self.config_enforce_cpu_var).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)

    def _create_hyperparameter_config_tab(self, parent):
        """创建超参数调优配置选项卡"""
        # 调优基本参数
        basic_frame = ttk.LabelFrame(parent, text="调优参数")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # 试验次数
        ttk.Label(basic_frame, text="试验次数:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.config_n_trials_var = tk.IntVar(value=50)
        ttk.Entry(basic_frame, textvariable=self.config_n_trials_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        # 调优策略
        ttk.Label(basic_frame, text="调优策略:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.config_strategy_var = tk.StringVar(value="TPE")
        strategy_combo = ttk.Combobox(basic_frame, textvariable=self.config_strategy_var,
                                    values=["TPE", "Random", "CmaEs"], state="readonly", width=12)
        strategy_combo.grid(row=0, column=3, padx=5, pady=5)

        # 超时时间
        ttk.Label(basic_frame, text="超时(分钟):").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.config_timeout_var = tk.IntVar(value=30)
        ttk.Entry(basic_frame, textvariable=self.config_timeout_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 评估指标
        ttk.Label(basic_frame, text="评估指标:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.config_metric_var = tk.StringVar(value="roc_auc")
        metric_combo = ttk.Combobox(basic_frame, textvariable=self.config_metric_var,
                                  values=["roc_auc", "accuracy", "f1", "precision", "recall"],
                                  state="readonly", width=12)
        metric_combo.grid(row=1, column=3, padx=5, pady=5)

    def _generate_config_file(self, config_type, mapping_tree, parent_window):
        """生成配置文件"""
        try:
            if config_type == "multi_data":
                # 多数据源配置
                items = mapping_tree.get_children()
                if not items:
                    messagebox.showerror("错误", "请至少添加一个模型-数据映射")
                    return

                # 构建配置
                model_data_mapping = {}
                for item in items:
                    values = mapping_tree.item(item)['values']
                    model_data_mapping[values[0]] = values[1]

                config = {
                    "description": "多数据源集成学习配置文件",
                    "created_by": "GUI配置生成器",
                    "created_at": datetime.now().isoformat(),
                    "config_type": "multi_data_ensemble",
                    "model_count": len(model_data_mapping),
                    "model_data_mapping": model_data_mapping,
                    "supported_ensemble_methods": ["voting", "stacking", "weighted"],
                    "supported_data_strategies": ["unified", "combined", "original"],
                    "usage_example": {
                        "command": "python main.py --model All --mode multi_data_ensemble --model_data_config <config_file>",
                        "description": "使用此配置文件运行多数据源集成学习"
                    }
                }

            elif config_type == "training":
                # 训练参数配置
                config = {
                    "description": "模型训练参数配置文件",
                    "created_by": "GUI配置生成器",
                    "created_at": datetime.now().isoformat(),
                    "config_type": "training_parameters",
                    "data_config": {
                        "test_size": self.training_test_size_var.get(),
                        "random_state": self.config_random_seed_var.get(),
                        "default_scaling": self.config_scaling_var.get(),
                        "cv_folds": self.config_cv_folds_var.get()
                    },
                    "training_config": {
                        "cv_folds": self.config_cv_folds_var.get(),
                        "n_jobs": -1,
                        "verbose": 1
                    },
                    "reproducibility_config": {
                        "strict_reproducibility": self.config_strict_repro_var.get(),
                        "num_threads": self.config_num_threads_var.get(),
                        "enforce_cpu": self.config_enforce_cpu_var.get()
                    }
                }

            elif config_type == "hyperparameter":
                # 超参数调优配置
                config = {
                    "description": "超参数调优配置文件",
                    "created_by": "GUI配置生成器",
                    "created_at": datetime.now().isoformat(),
                    "config_type": "hyperparameter_tuning",
                    "tuning_config": {
                        "n_trials": self.config_n_trials_var.get(),
                        "strategy": self.config_strategy_var.get(),
                        "timeout": self.config_timeout_var.get() * 60,  # 转换为秒
                        "metric": self.config_metric_var.get(),
                        "n_jobs": 1,
                        "early_stopping": True,
                        "patience": 10,
                        "min_improvement": 0.001
                    }
                }

            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                parent=parent_window,
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配置文件已保存到:\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"生成配置文件失败: {str(e)}")

    def _preview_config(self, config_type, mapping_tree):
        """预览配置内容"""
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("配置预览")
            preview_window.geometry("600x500")
            preview_window.transient(self.root)
            preview_window.grab_set()

            # 预览文本区域
            preview_text = tk.Text(preview_window, wrap=tk.WORD, font=('Consolas', 10))
            preview_scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=preview_text.yview)
            preview_text.configure(yscrollcommand=preview_scrollbar.set)

            preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 生成预览内容
            if config_type == "multi_data":
                items = mapping_tree.get_children()
                model_data_mapping = {}
                for item in items:
                    values = mapping_tree.item(item)['values']
                    model_data_mapping[values[0]] = values[1]

                config = {
                    "description": "多数据源集成学习配置文件",
                    "config_type": "multi_data_ensemble",
                    "model_data_mapping": model_data_mapping
                }
            else:
                config = {"config_type": config_type, "description": f"{config_type}配置预览"}

            # 显示配置内容
            config_json = json.dumps(config, indent=2, ensure_ascii=False)
            preview_text.insert(tk.END, config_json)

        except Exception as e:
            messagebox.showerror("错误", f"预览配置失败: {str(e)}")

    def show_help(self):
        messagebox.showinfo("帮助", "多模型集成机器学习平台\n\n使用说明:\n1. 加载数据文件\n2. 选择要训练的模型\n3. 配置训练参数\n4. 开始训练\n5. 查看结果和可视化")

    def show_about(self):
        messagebox.showinfo("关于", "多模型集成机器学习平台 v1.0\n\n基于Python和Tkinter开发\n支持多种机器学习算法")

    # 会话管理相关方法
    def show_session_manager(self):
        """显示会话管理器"""
        if self.session_manager_gui:
            try:
                self.session_manager_gui.show_session_manager()
            except Exception as e:
                messagebox.showerror("错误", f"打开会话管理器失败: {e}")
        else:
            messagebox.showwarning("警告", "会话管理器未初始化")

    def create_new_session(self):
        """创建新会话的快捷方法"""
        try:
            from datetime import datetime
            session_name = f"训练会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            session_id = create_new_session(session_name, "通过GUI创建的训练会话", auto_activate=True)

            if session_id:
                messagebox.showinfo("成功", f"新会话已创建并激活\n会话名称: {session_name}\n会话ID: {session_id}")
                self.update_session_status()
            else:
                messagebox.showerror("错误", "创建会话失败")
        except Exception as e:
            messagebox.showerror("错误", f"创建会话失败: {e}")

    def auto_create_session(self):
        """自动为当前训练创建会话"""
        try:
            # 检查是否已有活动会话
            current_session_id = get_active_session_id()
            if current_session_id:
                result = messagebox.askyesno("确认", "已有活动会话，是否创建新会话？")
                if not result:
                    return

            # 基于当前数据文件创建会话名称
            data_path = self.current_data_path.get()
            if data_path:
                data_name = Path(data_path).stem
                session_name = f"训练_{data_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                description = f"基于数据文件 {data_name} 的自动训练会话"
            else:
                session_name = f"自动训练会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                description = "自动创建的训练会话"

            session_id = create_new_session(session_name, description, auto_activate=True)

            if session_id:
                messagebox.showinfo("成功", f"自动会话已创建并激活\n会话名称: {session_name}")
                self.update_session_status()
            else:
                messagebox.showerror("错误", "创建自动会话失败")

        except Exception as e:
            messagebox.showerror("错误", f"创建自动会话失败: {e}")

    def update_session_status(self):
        """更新会话状态显示"""
        try:
            current_session_id = get_active_session_id()
            if current_session_id:
                # 更新窗口标题显示当前会话
                base_title = "多模型集成机器学习平台"
                self.root.title(f"{base_title} - 会话: {current_session_id[:12]}...")
            else:
                self.root.title("多模型集成机器学习平台")
        except Exception as e:
            self.logger.warning(f"更新会话状态失败: {e}")

    def refresh_model_options(self):
        """刷新模型选择选项"""
        try:
            # 检查缓存中可用的模型
            from config import CACHE_PATH, MODEL_NAMES
            available_models = []

            for model_name in MODEL_NAMES:
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                if cache_file.exists():
                    available_models.append(model_name)

            # 更新可视化模型选择下拉框
            if hasattr(self, 'viz_model_var') and hasattr(self, 'viz_model_combo'):
                current_value = self.viz_model_var.get()
                self.viz_model_combo['values'] = available_models

                # 如果当前选择的模型不在可用列表中，选择第一个可用模型
                if current_value not in available_models and available_models:
                    self.viz_model_var.set(available_models[0])
                elif not available_models:
                    self.viz_model_var.set("")

            self.logger.info(f"刷新模型选项完成，可用模型: {len(available_models)} 个")

        except Exception as e:
            self.logger.warning(f"刷新模型选项失败: {e}")

    def refresh_visualization_options(self):
        """刷新可视化选项"""
        try:
            # 刷新模型选项
            self.refresh_model_options()

            # 如果有可用模型，启用可视化按钮
            from config import CACHE_PATH, MODEL_NAMES
            has_models = any((CACHE_PATH / f"{model}_results.joblib").exists() for model in MODEL_NAMES)

            # 更新按钮状态（如果存在的话）
            if hasattr(self, 'viz_buttons'):
                for button in self.viz_buttons:
                    button.config(state=tk.NORMAL if has_models else tk.DISABLED)

            self.logger.info(f"刷新可视化选项完成，模型可用: {has_models}")

        except Exception as e:
            self.logger.warning(f"刷新可视化选项失败: {e}")

    def _perform_ensemble_delong_test(self, ensemble_results, X_test, y_test):
        """对集成学习结果进行DeLong检验"""
        try:
            from delong_test import perform_delong_comparison

            # 准备模型数据
            model_data = {}
            for model_name, model_info in ensemble_results.items():
                model = model_info.get('model')
                if model is None:
                    continue

                # 获取预测概率 - 修复硬投票集成模型的predict_proba误判问题
                y_score = None
                try:
                    # 优先尝试predict_proba
                    if hasattr(model, 'predict_proba'):
                        proba_result = model.predict_proba(X_test)
                        y_score = proba_result[:, 1] if proba_result.shape[1] > 1 else proba_result.flatten()
                except Exception:
                    # predict_proba失败时回退到decision_function
                    try:
                        if hasattr(model, 'decision_function'):
                            y_score = model.decision_function(X_test)
                    except Exception:
                        pass  # 两种方法都失败，跳过该模型

                if y_score is None:
                    continue  # 跳过无法获取概率的模型

                model_data[model_name] = {
                    'y_true': y_test,
                    'y_score': y_score
                }

            if len(model_data) < 2:
                self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "需要至少2个支持概率预测的集成模型才能进行DeLong检验\n")
                return

            # 执行DeLong检验
            comparison_results = perform_delong_comparison(model_data)

            # 显示结果
            self._display_ensemble_delong_results(comparison_results)

        except ImportError:
            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, "DeLong检验模块未找到\n")
        except Exception as e:
            self.update_gui_safely(self.ensemble_result_text, 'insert', tk.END, f"DeLong检验执行失败: {e}\n")

    def _display_ensemble_delong_results(self, results):
        """显示集成学习DeLong检验结果"""
        import tkinter as tk
        from tkinter import ttk

        # 创建结果显示窗口
        result_window = tk.Toplevel(self.root)
        result_window.title("集成学习DeLong检验结果")
        result_window.geometry("800x600")

        # 创建文本框显示结果
        text_frame = ttk.Frame(result_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 格式化并显示结果
        result_text = "🔬 集成学习DeLong检验结果 - 模型ROC曲线比较\n"
        result_text += "=" * 60 + "\n\n"

        if 'summary' in results:
            result_text += "📊 总结:\n"
            result_text += f"共比较了 {results['summary']['total_comparisons']} 对集成模型\n"
            result_text += f"显著差异的比较: {results['summary']['significant_comparisons']}\n\n"

        if 'pairwise_results' in results:
            result_text += "📈 两两比较结果:\n"
            result_text += "-" * 40 + "\n"

            for comparison in results['pairwise_results']:
                model1 = comparison['model1']
                model2 = comparison['model2']
                auc1 = comparison['auc1']
                auc2 = comparison['auc2']
                p_value = comparison['p_value']
                is_significant = comparison['is_significant']

                result_text += f"\n🆚 {model1} vs {model2}:\n"
                result_text += f"   AUC1: {auc1:.4f}\n"
                result_text += f"   AUC2: {auc2:.4f}\n"
                result_text += f"   AUC差异: {abs(auc1 - auc2):.4f}\n"
                result_text += f"   p值: {p_value:.6f}\n"
                result_text += f"   显著性: {'是' if is_significant else '否'} (α=0.05)\n"

                if is_significant:
                    better_model = model1 if auc1 > auc2 else model2
                    result_text += f"   🏆 {better_model} 显著优于另一个模型\n"
                else:
                    result_text += f"   ⚖️ 两个模型性能无显著差异\n"

        result_text += "\n" + "=" * 60 + "\n"
        result_text += "注: DeLong检验用于比较两个ROC曲线的AUC是否存在显著差异\n"
        result_text += "p < 0.05 表示两个模型的性能存在显著差异"

        text_widget.insert(tk.END, result_text)
        text_widget.config(state=tk.DISABLED)

        # 添加关闭按钮
        button_frame = ttk.Frame(result_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="关闭",
                  command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

    def run(self):
        """启动GUI应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MLPlatformGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"启动GUI失败: {e}")
        sys.exit(1)


# 为MultiModelApp类添加增强版多数据源集成学习相关方法
def add_enhanced_ensemble_methods(cls):
    """为MultiModelApp类添加增强版多数据源集成学习相关方法"""

    def add_data_source(self):
        """添加数据源"""
        try:
            # 创建添加数据源的对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("添加数据源")
            dialog.geometry("500x200")
            dialog.transient(self.root)
            dialog.grab_set()

            # 模型名称
            ttk.Label(dialog, text="模型名称:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
            model_name_var = tk.StringVar()
            model_combo = ttk.Combobox(dialog, textvariable=model_name_var, width=20)
            model_combo['values'] = MODEL_NAMES
            model_combo.grid(row=0, column=1, padx=10, pady=10)

            # 数据路径
            ttk.Label(dialog, text="数据路径:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
            data_path_var = tk.StringVar()
            ttk.Entry(dialog, textvariable=data_path_var, width=40).grid(row=1, column=1, padx=10, pady=10)

            def browse_data():
                filename = filedialog.askopenfilename(
                    title="选择数据文件",
                    filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
                if filename:
                    data_path_var.set(filename)

            ttk.Button(dialog, text="浏览...", command=browse_data).grid(row=1, column=2, padx=5, pady=10)

            # 按钮
            button_frame = ttk.Frame(dialog)
            button_frame.grid(row=2, column=0, columnspan=3, pady=20)

            def add_source():
                model_name = model_name_var.get().strip()
                data_path = data_path_var.get().strip()

                if not model_name or not data_path:
                    messagebox.showerror("错误", "请填写完整信息")
                    return

                if not Path(data_path).exists():
                    messagebox.showerror("错误", "数据文件不存在")
                    return

                # 添加到数据源列表
                self.data_sources[model_name] = data_path
                self.refresh_data_source_list()
                dialog.destroy()
                self.log_message(f"已添加数据源: {model_name} -> {data_path}")

            ttk.Button(button_frame, text="添加", command=add_source).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"添加数据源失败: {e}")

    def remove_data_source(self):
        """删除选中的数据源"""
        try:
            selected = self.data_source_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请选择要删除的数据源")
                return

            for item in selected:
                model_name = self.data_source_tree.item(item)['values'][0]
                if model_name in self.data_sources:
                    del self.data_sources[model_name]
                    self.log_message(f"已删除数据源: {model_name}")

            self.refresh_data_source_list()

        except Exception as e:
            messagebox.showerror("错误", f"删除数据源失败: {e}")

    def clear_data_sources(self):
        """清空所有数据源"""
        try:
            if messagebox.askyesno("确认", "确定要清空所有数据源吗？"):
                self.data_sources.clear()
                self.refresh_data_source_list()
                self.log_message("已清空所有数据源")
        except Exception as e:
            messagebox.showerror("错误", f"清空数据源失败: {e}")

    def refresh_data_source_list(self):
        """刷新数据源列表显示"""
        try:
            # 清空现有项目
            for item in self.data_source_tree.get_children():
                self.data_source_tree.delete(item)

            # 添加数据源
            for model_name, data_path in self.data_sources.items():
                self.data_source_tree.insert('', 'end', values=(model_name, data_path))

        except Exception as e:
            self.log_message(f"刷新数据源列表失败: {e}")

    def start_enhanced_multi_data_ensemble(self):
        """开始增强版多数据源集成学习"""
        try:
            # 检查数据源
            if not self.data_sources:
                messagebox.showerror("错误", "请至少添加一个数据源")
                return

            # 获取选中的融合方法
            selected_methods = [method for method, var in self.fusion_methods.items() if var.get()]
            if not selected_methods:
                messagebox.showerror("错误", "请至少选择一种融合方法")
                return

            # 获取校准方法
            calibration_method = self.calibration_method_var.get()

            # 在后台线程中运行
            def run_enhanced_ensemble():
                try:
                    self.status_text.set("正在运行增强版多数据源集成学习...")

                    # 导入并运行增强版多数据源集成学习
                    from enhanced_multi_data_ensemble import run_enhanced_multi_data_ensemble_pipeline

                    # 运行集成学习
                    results = run_enhanced_multi_data_ensemble_pipeline(
                        model_data_mapping=self.data_sources,
                        fusion_methods=selected_methods,
                        calibration_method=calibration_method,
                        enable_shap=True
                    )

                    if results:
                        self.current_ensemble_results = results
                        self.status_text.set("增强版多数据源集成学习完成")
                        self.log_message("增强版多数据源集成学习成功完成")
                        messagebox.showinfo("成功", "增强版多数据源集成学习完成！")
                    else:
                        self.status_text.set("增强版多数据源集成学习失败")
                        self.log_message("增强版多数据源集成学习失败")
                        messagebox.showerror("错误", "增强版多数据源集成学习失败")

                except Exception as e:
                    self.status_text.set("增强版多数据源集成学习失败")
                    self.log_message(f"增强版多数据源集成学习失败: {e}")
                    messagebox.showerror("错误", f"增强版多数据源集成学习失败: {e}")

            # 在新线程中运行
            thread = threading.Thread(target=run_enhanced_ensemble, daemon=True)
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动增强版多数据源集成学习失败: {e}")
            self.log_message(f"启动增强版多数据源集成学习失败: {e}")

    def visualize_ensemble_results(self):
        """可视化集成学习结果"""
        try:
            if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
                messagebox.showwarning("警告", "没有可用的集成学习结果")
                return

            # 创建结果展示窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("集成学习结果")
            result_window.geometry("800x600")

            # 创建表格显示结果
            columns = ('method', 'accuracy', 'precision', 'recall', 'f1_score', 'auc')
            tree = ttk.Treeview(result_window, columns=columns, show='headings')

            tree.heading('method', text='融合方法')
            tree.heading('accuracy', text='准确率')
            tree.heading('precision', text='精确率')
            tree.heading('recall', text='召回率')
            tree.heading('f1_score', text='F1分数')
            tree.heading('auc', text='AUC')

            # 添加数据
            results = self.current_ensemble_results.get('results', {})
            for method, result in results.items():
                if result.get('status') == 'success':
                    metrics = result['metrics']
                    tree.insert('', 'end', values=(
                        method,
                        f"{metrics['accuracy']:.4f}",
                        f"{metrics['precision']:.4f}",
                        f"{metrics['recall']:.4f}",
                        f"{metrics['f1_score']:.4f}",
                        f"{metrics['auc']:.4f}"
                    ))

            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 添加按钮
            button_frame = ttk.Frame(result_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="打开结果文件夹",
                      command=lambda: self.open_folder(self.current_ensemble_results.get('output_dir'))).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭", command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"可视化集成学习结果失败: {e}")

    def view_ensemble_shap(self):
        """查看集成学习SHAP分析"""
        try:
            if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
                messagebox.showwarning("警告", "没有可用的集成学习结果")
                return

            output_dir = self.current_ensemble_results.get('output_dir')
            if output_dir:
                shap_dir = Path(output_dir) / 'shap_analysis'
                if shap_dir.exists():
                    self.open_folder(shap_dir)
                else:
                    messagebox.showwarning("警告", "SHAP分析结果不存在")
            else:
                messagebox.showwarning("警告", "无法找到结果目录")

        except Exception as e:
            messagebox.showerror("错误", f"查看SHAP分析失败: {e}")

    def open_folder(self, folder_path):
        """打开文件夹"""
        try:
            import os
            import subprocess
            import platform

            folder_path = Path(folder_path)
            if not folder_path.exists():
                messagebox.showerror("错误", "文件夹不存在")
                return

            system = platform.system()
            if system == "Windows":
                os.startfile(folder_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹失败: {e}")

    # 将方法添加到类中
    cls.add_data_source = add_data_source
    cls.remove_data_source = remove_data_source
    cls.clear_data_sources = clear_data_sources
    cls.refresh_data_source_list = refresh_data_source_list
    cls.start_enhanced_multi_data_ensemble = start_enhanced_multi_data_ensemble
    cls.visualize_ensemble_results = visualize_ensemble_results
    cls.view_ensemble_shap = view_ensemble_shap
    cls.open_folder = open_folder

    return cls


# 应用增强方法到MLPlatformGUI类
MLPlatformGUI = add_enhanced_ensemble_methods(MLPlatformGUI)


if __name__ == "__main__":
    main()
