﻿模型组合,组合大小,平均性能得分,平均Q统计量,平均Q统计量多样性,平均不一致性,平均双错度量,平均双错多样性,平均相关系数,平均相关性多样性,综合多样性得分,综合得分,多样性等级,推荐集成方法
DecisionTree + NeuralNet,2,0.8521,0.0345,0.9655,0.275,0.025,0.975,0.4551,0.5449,0.6761,0.7641,良好,加权平均
DecisionTree + XGBoost,2,0.7985,0.0323,0.9677,0.35,0.05,0.95,0.3145,0.6855,0.7224,0.7604,优秀,简单投票法
DecisionTree + Logistic,2,0.8636,0.0943,0.9057,0.25,0.025,0.975,0.5103,0.4897,0.6396,0.7516,良好,加权平均
DecisionTree + <PERSON><PERSON>,2,0.8369,0.2353,0.7647,0.325,0.025,0.975,0.354,0.646,0.6511,0.744,良好,加权平均
DecisionTree + CatBoost,2,0.8212,0.3151,0.6849,0.35,0.025,0.975,0.3062,0.6938,0.6442,0.7327,良好,加权平均
DecisionTree + LightGBM,2,0.8094,0.3846,0.6154,0.375,0.025,0.975,0.2582,0.7418,0.6405,0.7249,良好,加权平均
DecisionTree + RandomForest,2,0.8359,0.2857,0.7143,0.275,0.05,0.95,0.4551,0.5449,0.5958,0.7158,良好,加权平均
DecisionTree + NaiveBayes,2,0.8786,0.7222,0.2778,0.175,0.05,0.95,0.6713,0.3287,0.3916,0.6351,中等,堆叠法
LightGBM + NaiveBayes,2,0.8418,0.8511,0.1489,0.2,0.075,0.925,0.5733,0.4267,0.375,0.6084,中等,堆叠法
CatBoost + NaiveBayes,2,0.8537,0.875,0.125,0.175,0.075,0.925,0.6325,0.3675,0.3485,0.6011,中等,堆叠法
SVM + NaiveBayes,2,0.8693,0.898,0.102,0.15,0.075,0.925,0.6921,0.3079,0.3222,0.5958,中等,堆叠法
RandomForest + NaiveBayes,2,0.8684,0.898,0.102,0.15,0.075,0.925,0.6921,0.3079,0.3222,0.5953,中等,堆叠法
NaiveBayes + NeuralNet,2,0.8845,0.9412,0.0588,0.1,0.075,0.925,0.7965,0.2035,0.2733,0.5789,较差,建议重新选择模型
Logistic + NaiveBayes,2,0.8961,0.9615,0.0385,0.075,0.075,0.925,0.8433,0.1567,0.2504,0.5732,较差,建议重新选择模型
XGBoost + NaiveBayes,2,0.8309,1.0,0.0,0.175,0.1,0.9,0.6225,0.3775,0.308,0.5695,中等,堆叠法
XGBoost + SVM,2,0.7892,0.8837,0.1163,0.175,0.15,0.85,0.6415,0.3585,0.3291,0.5592,中等,堆叠法
LightGBM + SVM,2,0.8002,0.9091,0.0909,0.15,0.15,0.85,0.6921,0.3079,0.3039,0.552,中等,堆叠法
XGBoost + Logistic,2,0.816,1.0,0.0,0.15,0.125,0.875,0.6847,0.3153,0.2831,0.5495,较差,建议重新选择模型
LightGBM + Logistic,2,0.8269,1.0,0.0,0.125,0.125,0.875,0.7379,0.2621,0.2649,0.5459,较差,建议重新选择模型
CatBoost + SVM,2,0.812,0.9333,0.0667,0.125,0.15,0.85,0.7433,0.2567,0.2788,0.5454,较差,建议重新选择模型
CatBoost + Logistic,2,0.8388,1.0,0.0,0.1,0.125,0.875,0.7917,0.2083,0.2467,0.5427,较差,建议重新选择模型
Logistic + SVM,2,0.8544,1.0,0.0,0.075,0.125,0.875,0.8465,0.1535,0.2282,0.5413,较差,建议重新选择模型
RandomForest + Logistic,2,0.8534,1.0,0.0,0.075,0.125,0.875,0.8465,0.1535,0.2282,0.5408,较差,建议重新选择模型
RandomForest + SVM,2,0.8267,0.9565,0.0435,0.1,0.15,0.85,0.7954,0.2046,0.254,0.5403,较差,建议重新选择模型
XGBoost + NeuralNet,2,0.8045,1.0,0.0,0.125,0.15,0.85,0.7475,0.2525,0.258,0.5312,较差,建议重新选择模型
Logistic + NeuralNet,2,0.8696,1.0,0.0,0.025,0.125,0.875,0.9497,0.0503,0.1926,0.5311,较差,建议重新选择模型
LightGBM + NeuralNet,2,0.8154,1.0,0.0,0.1,0.15,0.85,0.7965,0.2035,0.2407,0.528,较差,建议重新选择模型
RandomForest + XGBoost,2,0.7883,0.96,0.04,0.125,0.175,0.825,0.7475,0.2525,0.265,0.5266,较差,建议重新选择模型
CatBoost + NeuralNet,2,0.8272,1.0,0.0,0.075,0.15,0.85,0.8465,0.1535,0.2232,0.5252,较差,建议重新选择模型
SVM + NeuralNet,2,0.8429,1.0,0.0,0.05,0.15,0.85,0.8977,0.1023,0.2055,0.5242,较差,建议重新选择模型
RandomForest + NeuralNet,2,0.8419,1.0,0.0,0.05,0.15,0.85,0.8977,0.1023,0.2055,0.5237,较差,建议重新选择模型
RandomForest + LightGBM,2,0.7992,0.9709,0.0291,0.1,0.175,0.825,0.7965,0.2035,0.2444,0.5218,较差,建议重新选择模型
RandomForest + CatBoost,2,0.811,0.9811,0.0189,0.075,0.175,0.825,0.8465,0.1535,0.2239,0.5174,较差,建议重新选择模型
XGBoost + CatBoost,2,0.7736,1.0,0.0,0.05,0.225,0.775,0.8987,0.1013,0.1903,0.4819,较差,建议重新选择模型
LightGBM + CatBoost,2,0.7845,1.0,0.0,0.025,0.225,0.775,0.9487,0.0513,0.1728,0.4786,较差,建议重新选择模型
XGBoost + LightGBM,2,0.7617,1.0,0.0,0.025,0.25,0.75,0.9473,0.0527,0.168,0.4649,较差,建议重新选择模型
