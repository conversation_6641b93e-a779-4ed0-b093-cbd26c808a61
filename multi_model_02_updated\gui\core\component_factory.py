"""
GUI组件工厂
提供统一的GUI组件创建接口
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class ComponentFactory:
    """GUI组件工厂类"""
    
    def __init__(self):
        """初始化组件工厂"""
        self._style = ttk.Style()
        self._setup_styles()
    
    def _setup_styles(self):
        """设置组件样式"""
        try:
            # 设置主题
            self._style.theme_use('clam')
            
            # 自定义样式
            self._style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
            self._style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))
            self._style.configure('Success.TLabel', foreground='green')
            self._style.configure('Error.TLabel', foreground='red')
            self._style.configure('Warning.TLabel', foreground='orange')
            
            # 按钮样式
            self._style.configure('Primary.TButton', font=('Arial', 9, 'bold'))
            self._style.configure('Success.TButton', background='#4CAF50')
            self._style.configure('Warning.TButton', background='#FF9800')
            self._style.configure('Error.TButton', background='#F44336')
            
        except Exception as e:
            logger.warning(f"设置样式失败: {e}")
    
    def create_frame(self, parent: tk.Widget, **kwargs) -> ttk.Frame:
        """
        创建框架
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            ttk.Frame: 框架组件
        """
        return ttk.Frame(parent, **kwargs)
    
    def create_label_frame(self, parent: tk.Widget, text: str, **kwargs) -> ttk.LabelFrame:
        """
        创建标签框架
        
        Args:
            parent: 父组件
            text: 标签文本
            **kwargs: 其他参数
            
        Returns:
            ttk.LabelFrame: 标签框架组件
        """
        return ttk.LabelFrame(parent, text=text, **kwargs)
    
    def create_label(self, parent: tk.Widget, text: str = "", 
                    style: str = None, **kwargs) -> ttk.Label:
        """
        创建标签
        
        Args:
            parent: 父组件
            text: 标签文本
            style: 样式名称
            **kwargs: 其他参数
            
        Returns:
            ttk.Label: 标签组件
        """
        if style:
            kwargs['style'] = style
        return ttk.Label(parent, text=text, **kwargs)
    
    def create_button(self, parent: tk.Widget, text: str, 
                     command: Callable = None, style: str = None, 
                     **kwargs) -> ttk.Button:
        """
        创建按钮
        
        Args:
            parent: 父组件
            text: 按钮文本
            command: 点击回调函数
            style: 样式名称
            **kwargs: 其他参数
            
        Returns:
            ttk.Button: 按钮组件
        """
        if style:
            kwargs['style'] = style
        if command:
            kwargs['command'] = command
        return ttk.Button(parent, text=text, **kwargs)
    
    def create_entry(self, parent: tk.Widget, textvariable: tk.StringVar = None,
                    **kwargs) -> ttk.Entry:
        """
        创建输入框
        
        Args:
            parent: 父组件
            textvariable: 文本变量
            **kwargs: 其他参数
            
        Returns:
            ttk.Entry: 输入框组件
        """
        if textvariable:
            kwargs['textvariable'] = textvariable
        return ttk.Entry(parent, **kwargs)
    
    def create_combobox(self, parent: tk.Widget, values: list = None,
                       textvariable: tk.StringVar = None, **kwargs) -> ttk.Combobox:
        """
        创建下拉框
        
        Args:
            parent: 父组件
            values: 选项列表
            textvariable: 文本变量
            **kwargs: 其他参数
            
        Returns:
            ttk.Combobox: 下拉框组件
        """
        if textvariable:
            kwargs['textvariable'] = textvariable
        if values:
            kwargs['values'] = values
        return ttk.Combobox(parent, **kwargs)
    
    def create_checkbutton(self, parent: tk.Widget, text: str,
                          variable: tk.BooleanVar = None, **kwargs) -> ttk.Checkbutton:
        """
        创建复选框
        
        Args:
            parent: 父组件
            text: 复选框文本
            variable: 布尔变量
            **kwargs: 其他参数
            
        Returns:
            ttk.Checkbutton: 复选框组件
        """
        if variable:
            kwargs['variable'] = variable
        return ttk.Checkbutton(parent, text=text, **kwargs)
    
    def create_radiobutton(self, parent: tk.Widget, text: str,
                          variable: tk.StringVar = None, value: str = None,
                          **kwargs) -> ttk.Radiobutton:
        """
        创建单选框
        
        Args:
            parent: 父组件
            text: 单选框文本
            variable: 字符串变量
            value: 选项值
            **kwargs: 其他参数
            
        Returns:
            ttk.Radiobutton: 单选框组件
        """
        if variable:
            kwargs['variable'] = variable
        if value:
            kwargs['value'] = value
        return ttk.Radiobutton(parent, text=text, **kwargs)
    
    def create_progressbar(self, parent: tk.Widget, **kwargs) -> ttk.Progressbar:
        """
        创建进度条
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            ttk.Progressbar: 进度条组件
        """
        return ttk.Progressbar(parent, **kwargs)
    
    def create_notebook(self, parent: tk.Widget, **kwargs) -> ttk.Notebook:
        """
        创建选项卡
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            ttk.Notebook: 选项卡组件
        """
        return ttk.Notebook(parent, **kwargs)
    
    def create_treeview(self, parent: tk.Widget, columns: list = None,
                       **kwargs) -> ttk.Treeview:
        """
        创建树形视图
        
        Args:
            parent: 父组件
            columns: 列定义
            **kwargs: 其他参数
            
        Returns:
            ttk.Treeview: 树形视图组件
        """
        if columns:
            kwargs['columns'] = columns
        return ttk.Treeview(parent, **kwargs)
    
    def create_scrollbar(self, parent: tk.Widget, **kwargs) -> ttk.Scrollbar:
        """
        创建滚动条
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            ttk.Scrollbar: 滚动条组件
        """
        return ttk.Scrollbar(parent, **kwargs)
    
    def create_text(self, parent: tk.Widget, **kwargs) -> tk.Text:
        """
        创建文本框
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            tk.Text: 文本框组件
        """
        return tk.Text(parent, **kwargs)
    
    def create_listbox(self, parent: tk.Widget, **kwargs) -> tk.Listbox:
        """
        创建列表框
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            tk.Listbox: 列表框组件
        """
        return tk.Listbox(parent, **kwargs)
    
    def create_canvas(self, parent: tk.Widget, **kwargs) -> tk.Canvas:
        """
        创建画布
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            tk.Canvas: 画布组件
        """
        return tk.Canvas(parent, **kwargs)


# 全局组件工厂实例
_component_factory = None


def get_component_factory() -> ComponentFactory:
    """获取全局组件工厂实例"""
    global _component_factory
    if _component_factory is None:
        _component_factory = ComponentFactory()
    return _component_factory
