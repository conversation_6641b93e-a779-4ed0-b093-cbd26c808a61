2025-08-23 17:39:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-24 16:52:17 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-25 23:34:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 16:39:09 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 17:06:09 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 17:24:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 17:41:41 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 18:44:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 18:45:22 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 18:58:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 19:17:22 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 19:38:40 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 19:48:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 20:04:58 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 20:08:09 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 20:13:16 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-28 23:39:35 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 00:05:25 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 00:42:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 01:09:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 01:39:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 02:32:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 03:02:14 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 03:18:44 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 04:21:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 08:27:38 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 08:45:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 08:55:21 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 08:56:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 09:08:06 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 00:14:57 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 01:32:18 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 01:43:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 01:53:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 02:01:52 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 02:17:08 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-30 02:19:50 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-02 23:18:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-02 23:19:33 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-02 23:33:30 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-03 00:28:07 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 21:44:52 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 21:48:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 22:08:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 22:17:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:11:43 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:21:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:25:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:50:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:51:50 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-04 23:56:32 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:01:23 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:07:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:16:17 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:19:21 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:26:04 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:45:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 00:57:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 01:28:21 - model_ensemble - INFO - ============================================================
2025-09-05 01:28:21 - model_ensemble - INFO - 开始运行集成学习管道
2025-09-05 01:28:21 - model_ensemble - INFO - ============================================================
2025-09-05 01:28:21 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'NeuralNet']
2025-09-05 01:28:21 - model_ensemble - INFO - 集成方法: ['voting', 'bagging', 'boosting', 'stacking']
2025-09-05 01:28:21 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-09-05 01:28:21 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-09-05 01:28:21 - model_ensemble - INFO -   DecisionTree 训练完成
2025-09-05 01:28:21 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-09-05 01:28:22 - model_ensemble - INFO -   NeuralNet 训练完成
2025-09-05 01:28:22 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-09-05 01:28:22 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-09-05 01:28:22 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-09-05 01:28:22 - model_ensemble - INFO -     正在优化 voting_soft 的权重...
2025-09-05 01:28:35 - model_ensemble - INFO - Voting权重优化完成: 最优CV f1_weighted=0.8914
2025-09-05 01:28:35 - model_ensemble - INFO -     使用优化权重: [0.14641385 0.85358615]
2025-09-05 01:28:35 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-09-05 01:28:36 - model_ensemble - INFO - 集成模型 voting_soft 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\voting_soft_ensemble_012836.joblib
2025-09-05 01:28:36 - model_ensemble - INFO - 集成模型 voting_soft 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Ensemble_voting_soft_results.joblib
2025-09-05 01:28:36 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8500
2025-09-05 01:28:36 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-09-05 01:28:36 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-09-05 01:28:37 - model_ensemble - INFO - 集成模型 voting_hard 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\voting_hard_ensemble_012837.joblib
2025-09-05 01:28:37 - model_ensemble - INFO - 集成模型 voting_hard 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Ensemble_voting_hard_results.joblib
2025-09-05 01:28:37 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8460
2025-09-05 01:28:37 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-09-05 01:28:37 - model_ensemble - INFO -     正在优化 bagging 的参数...
2025-09-05 01:28:43 - model_ensemble - INFO - Bagging参数优化完成: 最优CV f1_weighted=0.8986
2025-09-05 01:28:43 - model_ensemble - INFO -     使用优化参数: {'n_estimators': 100, 'max_samples': 0.6, 'max_features': 0.7, 'bootstrap': False}
2025-09-05 01:28:43 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-09-05 01:28:44 - model_ensemble - INFO - 集成模型 bagging 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\bagging_ensemble_012844.joblib
2025-09-05 01:28:44 - model_ensemble - INFO - 集成模型 bagging 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Ensemble_bagging_results.joblib
2025-09-05 01:28:44 - model_ensemble - INFO -   bagging - 准确率: 0.9000, F1: 0.9005
2025-09-05 01:28:44 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-09-05 01:28:44 - model_ensemble - INFO -     正在优化 boosting 的参数...
2025-09-05 01:28:50 - model_ensemble - INFO - AdaBoost参数优化完成: 最优CV f1_weighted=0.9235
2025-09-05 01:28:50 - model_ensemble - INFO -     使用优化参数: {'n_estimators': 200, 'learning_rate': 0.5}
2025-09-05 01:28:50 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-09-05 01:28:52 - model_ensemble - INFO - 集成模型 boosting 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\boosting_ensemble_012851.joblib
2025-09-05 01:28:52 - model_ensemble - INFO - 集成模型 boosting 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Ensemble_boosting_results.joblib
2025-09-05 01:28:52 - model_ensemble - INFO -   boosting - 准确率: 0.8250, F1: 0.8242
2025-09-05 01:28:52 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-09-05 01:28:52 - model_ensemble - INFO -     正在优化 stacking 的参数...
2025-09-05 01:28:52 - model_ensemble - WARNING - Stacking参数优化失败: name 'StackingClassifier' is not defined
2025-09-05 01:28:52 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-09-05 01:28:53 - model_ensemble - INFO - 集成模型 stacking 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\stacking_ensemble_012853.joblib
2025-09-05 01:28:53 - model_ensemble - INFO - 集成模型 stacking 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Ensemble_stacking_results.joblib
2025-09-05 01:28:53 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-09-05 01:28:53 - model_ensemble - INFO - ============================================================
2025-09-05 01:28:53 - model_ensemble - INFO - 集成学习结果总结
2025-09-05 01:28:53 - model_ensemble - INFO - ============================================================
2025-09-05 01:28:53 - model_ensemble - INFO - 最佳集成模型: bagging
2025-09-05 01:28:53 - model_ensemble - INFO - 最佳F1分数: 0.9005
2025-09-05 01:28:53 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-09-05 01:28:53 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-09-05 01:28:53 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8500, 召回率: 0.8500, F1: 0.8500, AUC: 0.9233
2025-09-05 01:28:53 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8608, 召回率: 0.8500, F1: 0.8460, AUC: 0.9361
2025-09-05 01:28:53 - model_ensemble - INFO -   bagging         - 准确率: 0.9000, 精确率: 0.9055, 召回率: 0.9000, F1: 0.9005, AUC: 0.9565
2025-09-05 01:28:53 - model_ensemble - INFO -   boosting        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.9003
2025-09-05 01:28:53 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9309
2025-09-05 01:28:53 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-09-05 01:28:54 - model_ensemble - INFO - 集成学习结果已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250905_010136\models\ensemble_20250905_012853_ensemble_012853.joblib
2025-09-05 01:28:55 - model_ensemble - WARNING - Failed to save ensemble report: main thread is not in main loop
2025-09-05 01:29:36 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 01:33:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 01:43:35 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 02:31:10 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 14:13:24 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 14:45:22 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 15:10:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 15:25:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-09-05 15:27:23 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
