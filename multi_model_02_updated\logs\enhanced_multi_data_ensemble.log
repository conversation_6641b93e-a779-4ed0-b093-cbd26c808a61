2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 开始运行增强版多数据源集成学习管道
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 模型数据映射: {'RandomForest': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_A.csv', 'LogisticRegression': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_B.csv', 'SVM': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_C.csv'}
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 融合方法: ['soft_voting', 'logit_weighted', 'stacking']
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 步骤1: 加载数据
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 开始加载多数据源...
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 加载 RandomForest 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_A.csv
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 加载 RandomForest 数据失败: tuple indices must be integers or slices, not str
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 加载 LogisticRegression 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_B.csv
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 加载 LogisticRegression 数据失败: tuple indices must be integers or slices, not str
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 加载 SVM 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_C.csv
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 加载 SVM 数据失败: tuple indices must be integers or slices, not str
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 步骤2: 训练基础模型
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 开始训练基础模型...
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 训练模型: RandomForest
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 训练模型 RandomForest 失败: tuple indices must be integers or slices, not str
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - WARNING - 未知的模型类型: LogisticRegression
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - INFO - 训练模型: SVM
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 训练模型 SVM 失败: tuple indices must be integers or slices, not str
2025-09-04 23:35:44 - enhanced_multi_data_ensemble - ERROR - 没有成功训练任何基础模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 开始运行增强版多数据源集成学习管道
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 模型数据映射: {'RandomForest': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_A.csv', 'LogisticRegression': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_B.csv', 'SVM': 'D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_C.csv'}
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 融合方法: ['soft_voting', 'logit_weighted', 'stacking']
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 步骤1: 加载数据
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 开始加载多数据源...
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 加载 RandomForest 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_A.csv
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 成功加载 RandomForest 数据，训练集大小: (800, 20)
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 加载 LogisticRegression 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_B.csv
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 成功加载 LogisticRegression 数据，训练集大小: (800, 25)
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 加载 SVM 的数据: D:\Code\MM01U\multi_model_02_updated\test_data\dataset_C.csv
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 成功加载 SVM 数据，训练集大小: (800, 20)
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 步骤2: 训练基础模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 开始训练基础模型...
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 训练模型: RandomForest
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 模型 RandomForest 训练完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 训练模型: LogisticRegression
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 模型 LogisticRegression 训练完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 训练模型: SVM
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 模型 SVM 训练完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 步骤3: 创建集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 开始创建集成模型...
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 创建 soft_voting 集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - soft_voting 集成模型创建完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 创建 logit_weighted 集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - logit_weighted 集成模型创建完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 创建 stacking 集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - stacking 集成模型创建完成
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 步骤4: 评估集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 开始评估集成模型...
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 评估 soft_voting 集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - soft_voting - 准确率: 0.9350, F1: 0.9350, AUC: 0.9761
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 评估 logit_weighted 集成模型
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - logit_weighted - 准确率: 0.9550, F1: 0.9550, AUC: 0.9895
2025-09-04 23:37:21 - enhanced_multi_data_ensemble - INFO - 评估 stacking 集成模型
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - stacking - 准确率: 0.9600, F1: 0.9600, AUC: 0.9851
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 步骤6: 保存结果
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 结果已保存到: D:\Code\MM01U\multi_model_02_updated\ensemble\enhanced_multi_data
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 集成学习结果摘要
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 最佳融合方法: stacking
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 最佳F1分数: 0.9600
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 最佳准确率: 0.9600
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 最佳AUC: 0.9851
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 
所有方法性能对比:
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO -   soft_voting          - 准确率: 0.9350, F1: 0.9350, AUC: 0.9761
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO -   logit_weighted       - 准确率: 0.9550, F1: 0.9550, AUC: 0.9895
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO -   stacking             - 准确率: 0.9600, F1: 0.9600, AUC: 0.9851
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 
详细结果已保存到: D:\Code\MM01U\multi_model_02_updated\ensemble\enhanced_multi_data\ensemble_results_20250904_233723.json
2025-09-04 23:37:23 - enhanced_multi_data_ensemble - INFO - 增强版多数据源集成学习管道完成
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 开始运行增强版多数据源集成学习管道
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - ============================================================
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 模型数据映射: {'NaiveBayes': 'C:/Users/<USER>/Desktop/parameters/0904/nodule_ct_1_D.csv', 'RandomForest': 'C:/Users/<USER>/Desktop/parameters/0904/VOCs_1.csv'}
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 融合方法: ['soft_voting', 'logit_weighted', 'stacking']
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 步骤1: 加载数据
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 开始加载多数据源...
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 加载 NaiveBayes 的数据: C:/Users/<USER>/Desktop/parameters/0904/nodule_ct_1_D.csv
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 成功加载 NaiveBayes 数据，训练集大小: (157, 5)
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 加载 RandomForest 的数据: C:/Users/<USER>/Desktop/parameters/0904/VOCs_1.csv
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 成功加载 RandomForest 数据，训练集大小: (157, 6)
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - INFO - 步骤2: 训练基础模型
2025-09-05 15:27:49 - enhanced_multi_data_ensemble - ERROR - 运行增强版多数据源集成学习管道失败: train_base_models() missing 1 required positional argument: 'model_config'
