"""
基础GUI类
提供GUI组件的通用功能和接口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any, Callable
import logging
from abc import ABC, abstractmethod

from .event_manager import EventMixin, EventType
from .config_manager import get_gui_config
from .component_factory import get_component_factory

logger = logging.getLogger(__name__)


class BaseGUI(EventMixin, ABC):
    """基础GUI类，提供通用功能"""
    
    def __init__(self, parent: Optional[tk.Widget] = None):
        """
        初始化基础GUI
        
        Args:
            parent: 父组件
        """
        super().__init__()
        self.parent = parent
        self.config = get_gui_config()
        self.factory = get_component_factory()
        
        # 初始化组件引用
        self.main_frame: Optional[ttk.Frame] = None
        self.status_var = tk.StringVar(value="就绪")
        
        # 设置UI
        self._setup_ui()
        
        logger.debug(f"{self.__class__.__name__} 初始化完成")
    
    @abstractmethod
    def _setup_ui(self):
        """设置用户界面（子类必须实现）"""
        pass
    
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        messagebox.showinfo(title, message)
    
    def show_warning(self, title: str, message: str):
        """显示警告对话框"""
        messagebox.showwarning(title, message)
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        messagebox.showerror(title, message)
    
    def ask_yes_no(self, title: str, message: str) -> bool:
        """显示是否确认对话框"""
        return messagebox.askyesno(title, message)
    
    def update_status(self, message: str):
        """更新状态信息"""
        self.status_var.set(message)
        logger.debug(f"状态更新: {message}")
        
        # 发布状态更新事件
        self.publish_event(EventType.STATUS_UPDATED, {"message": message})
    
    def create_scrollable_frame(self, parent: tk.Widget) -> tuple[ttk.Frame, tk.Canvas, ttk.Scrollbar]:
        """
        创建可滚动的框架
        
        Args:
            parent: 父组件
            
        Returns:
            tuple: (scrollable_frame, canvas, scrollbar)
        """
        # 创建画布和滚动条
        canvas = self.factory.create_canvas(parent)
        scrollbar = self.factory.create_scrollbar(parent, orient="vertical", command=canvas.yview)
        
        # 创建可滚动框架
        scrollable_frame = self.factory.create_frame(canvas)
        
        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        return scrollable_frame, canvas, scrollbar
    
    def create_button_group(self, parent: tk.Widget, buttons: list[dict],
                           orientation: str = "horizontal") -> list[ttk.Button]:
        """
        创建按钮组
        
        Args:
            parent: 父组件
            buttons: 按钮配置列表，每个元素包含 text, command, style 等
            orientation: 布局方向 ("horizontal" 或 "vertical")
            
        Returns:
            list[ttk.Button]: 按钮列表
        """
        button_widgets = []
        
        for i, btn_config in enumerate(buttons):
            btn = self.factory.create_button(
                parent,
                text=btn_config.get("text", f"按钮{i+1}"),
                command=btn_config.get("command"),
                style=btn_config.get("style")
            )
            
            if orientation == "horizontal":
                btn.pack(side=tk.LEFT, padx=2, pady=2)
            else:
                btn.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
            
            button_widgets.append(btn)
        
        return button_widgets
    
    def create_form_field(self, parent: tk.Widget, label_text: str,
                         field_type: str = "entry", **kwargs) -> tuple[ttk.Label, tk.Widget]:
        """
        创建表单字段
        
        Args:
            parent: 父组件
            label_text: 标签文本
            field_type: 字段类型 ("entry", "combobox", "checkbutton", etc.)
            **kwargs: 字段参数
            
        Returns:
            tuple: (label, field_widget)
        """
        # 创建标签
        label = self.factory.create_label(parent, text=label_text)
        
        # 创建字段组件
        if field_type == "entry":
            field = self.factory.create_entry(parent, **kwargs)
        elif field_type == "combobox":
            field = self.factory.create_combobox(parent, **kwargs)
        elif field_type == "checkbutton":
            field = self.factory.create_checkbutton(parent, text="", **kwargs)
        elif field_type == "text":
            field = self.factory.create_text(parent, **kwargs)
        else:
            raise ValueError(f"不支持的字段类型: {field_type}")
        
        return label, field
    
    def bind_validation(self, widget: tk.Widget, validator: Callable[[str], bool],
                       error_message: str = "输入无效"):
        """
        为组件绑定验证
        
        Args:
            widget: 要验证的组件
            validator: 验证函数
            error_message: 错误消息
        """
        def validate(event=None):
            try:
                if hasattr(widget, 'get'):
                    value = widget.get()
                    if not validator(value):
                        self.show_error("验证错误", error_message)
                        return False
                return True
            except Exception as e:
                logger.error(f"验证失败: {e}")
                return False
        
        # 绑定失去焦点事件
        widget.bind("<FocusOut>", validate)
        
        return validate
    
    def safe_execute(self, func: Callable, *args, error_title: str = "错误", **kwargs):
        """
        安全执行函数，捕获异常并显示错误对话框
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            error_title: 错误对话框标题
            **kwargs: 函数关键字参数
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"执行函数失败 {func.__name__}: {e}")
            self.show_error(error_title, f"操作失败: {str(e)}")
            return None
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set_config_value(self, key: str, value: Any):
        """设置配置值"""
        self.config.set(key, value)
    
    def save_config(self):
        """保存配置"""
        self.config.save()


class DialogBase(BaseGUI):
    """对话框基类"""
    
    def __init__(self, parent: tk.Widget, title: str = "对话框",
                 modal: bool = True, resizable: bool = True):
        """
        初始化对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            modal: 是否为模态对话框
            resizable: 是否可调整大小
        """
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        
        if modal:
            self.dialog.grab_set()
        
        if not resizable:
            self.dialog.resizable(False, False)
        
        # 居中显示
        self._center_dialog()
        
        super().__init__(self.dialog)
        
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def _center_dialog(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def on_close(self):
        """关闭对话框"""
        self.dialog.destroy()
    
    def show(self):
        """显示对话框"""
        self.dialog.wait_window()


class ProgressDialog(DialogBase):
    """进度对话框"""
    
    def __init__(self, parent: tk.Widget, title: str = "处理中...",
                 message: str = "请稍候..."):
        """
        初始化进度对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            message: 进度消息
        """
        self.message = message
        super().__init__(parent, title, modal=True, resizable=False)
    
    def _setup_ui(self):
        """设置进度对话框UI"""
        self.main_frame = self.factory.create_frame(self.dialog)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 消息标签
        self.message_label = self.factory.create_label(
            self.main_frame, text=self.message
        )
        self.message_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = self.factory.create_progressbar(
            self.main_frame, variable=self.progress_var, length=300
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # 取消按钮
        self.cancel_button = self.factory.create_button(
            self.main_frame, text="取消", command=self.on_cancel
        )
        self.cancel_button.pack()
        
        self.cancelled = False
    
    def update_progress(self, value: float, message: str = None):
        """
        更新进度
        
        Args:
            value: 进度值 (0-100)
            message: 进度消息
        """
        self.progress_var.set(value)
        if message:
            self.message_label.config(text=message)
        self.dialog.update()
    
    def on_cancel(self):
        """取消操作"""
        self.cancelled = True
        self.on_close()
