﻿模型对,Q统计量,Q统计量多样性,不一致性度量,双错度量,双错多样性,相关系数,相关性多样性,综合多样性得分,多样性等级
DecisionTree vs RandomForest,0.2857,0.7143,0.275,0.05,0.95,0.4551,0.5449,0.5958,良好
DecisionTree vs XGBoost,0.0323,0.9677,0.35,0.05,0.95,0.3145,0.6855,0.7224,优秀
DecisionTree vs LightGBM,-0.3846,0.6154,0.375,0.025,0.975,0.2582,0.7418,0.6405,良好
DecisionTree vs CatBoost,-0.3151,0.6849,0.35,0.025,0.975,0.3062,0.6938,0.6442,良好
DecisionTree vs Logistic,0.0943,0.9057,0.25,0.025,0.975,0.5103,0.4897,0.6396,良好
DecisionTree vs SVM,-0.2353,0.7647,0.325,0.025,0.975,0.354,0.646,0.6511,良好
Decision<PERSON><PERSON> vs Na<PERSON><PERSON><PERSON><PERSON>,0.7222,0.2778,0.175,0.05,0.95,0.6713,0.3287,0.3916,中等
DecisionTree vs NeuralNet,-0.0345,0.9655,0.275,0.025,0.975,0.4551,0.5449,0.6761,良好
<PERSON>Forest vs XGBoost,0.96,0.04,0.125,0.175,0.825,0.7475,0.2525,0.265,较差
RandomForest vs LightGBM,0.9709,0.0291,0.1,0.175,0.825,0.7965,0.2035,0.2444,较差
RandomForest vs CatBoost,0.9811,0.0189,0.075,0.175,0.825,0.8465,0.1535,0.2239,较差
RandomForest vs Logistic,1.0,0.0,0.075,0.125,0.875,0.8465,0.1535,0.2282,较差
RandomForest vs SVM,0.9565,0.0435,0.1,0.15,0.85,0.7954,0.2046,0.254,较差
RandomForest vs NaiveBayes,0.898,0.102,0.15,0.075,0.925,0.6921,0.3079,0.3222,中等
RandomForest vs NeuralNet,1.0,0.0,0.05,0.15,0.85,0.8977,0.1023,0.2055,较差
XGBoost vs LightGBM,1.0,0.0,0.025,0.25,0.75,0.9473,0.0527,0.168,较差
XGBoost vs CatBoost,1.0,0.0,0.05,0.225,0.775,0.8987,0.1013,0.1903,较差
XGBoost vs Logistic,1.0,0.0,0.15,0.125,0.875,0.6847,0.3153,0.2831,较差
XGBoost vs SVM,0.8837,0.1163,0.175,0.15,0.85,0.6415,0.3585,0.3291,中等
XGBoost vs NaiveBayes,1.0,0.0,0.175,0.1,0.9,0.6225,0.3775,0.308,中等
XGBoost vs NeuralNet,1.0,0.0,0.125,0.15,0.85,0.7475,0.2525,0.258,较差
LightGBM vs CatBoost,1.0,0.0,0.025,0.225,0.775,0.9487,0.0513,0.1728,较差
LightGBM vs Logistic,1.0,0.0,0.125,0.125,0.875,0.7379,0.2621,0.2649,较差
LightGBM vs SVM,0.9091,0.0909,0.15,0.15,0.85,0.6921,0.3079,0.3039,中等
LightGBM vs NaiveBayes,0.8511,0.1489,0.2,0.075,0.925,0.5733,0.4267,0.375,中等
LightGBM vs NeuralNet,1.0,0.0,0.1,0.15,0.85,0.7965,0.2035,0.2407,较差
CatBoost vs Logistic,1.0,0.0,0.1,0.125,0.875,0.7917,0.2083,0.2467,较差
CatBoost vs SVM,0.9333,0.0667,0.125,0.15,0.85,0.7433,0.2567,0.2788,较差
CatBoost vs NaiveBayes,0.875,0.125,0.175,0.075,0.925,0.6325,0.3675,0.3485,中等
CatBoost vs NeuralNet,1.0,0.0,0.075,0.15,0.85,0.8465,0.1535,0.2232,较差
Logistic vs SVM,1.0,0.0,0.075,0.125,0.875,0.8465,0.1535,0.2282,较差
Logistic vs NaiveBayes,0.9615,0.0385,0.075,0.075,0.925,0.8433,0.1567,0.2504,较差
Logistic vs NeuralNet,1.0,0.0,0.025,0.125,0.875,0.9497,0.0503,0.1926,较差
SVM vs NaiveBayes,0.898,0.102,0.15,0.075,0.925,0.6921,0.3079,0.3222,中等
SVM vs NeuralNet,1.0,0.0,0.05,0.15,0.85,0.8977,0.1023,0.2055,较差
NaiveBayes vs NeuralNet,0.9412,0.0588,0.1,0.075,0.925,0.7965,0.2035,0.2733,较差
