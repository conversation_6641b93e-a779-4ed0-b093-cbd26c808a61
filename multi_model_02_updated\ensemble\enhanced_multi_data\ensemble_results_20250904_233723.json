{"timestamp": "20250904_233723", "model_data_mapping": {"RandomForest": "D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_A.csv", "LogisticRegression": "D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_B.csv", "SVM": "D:\\Code\\MM01U\\multi_model_02_updated\\test_data\\dataset_C.csv"}, "fusion_methods": ["soft_voting", "logit_weighted", "stacking"], "results_summary": {"soft_voting": {"accuracy": 0.935, "precision": 0.935043504350435, "recall": 0.935, "f1_score": 0.934998374959374, "auc": 0.9761}, "logit_weighted": {"accuracy": 0.955, "precision": 0.955045504550455, "recall": 0.955, "f1_score": 0.9549988749718743, "auc": 0.9895}, "stacking": {"accuracy": 0.96, "precision": 0.9601840736294517, "recall": 0.96, "f1_score": 0.9599959995999601, "auc": 0.9851}}}