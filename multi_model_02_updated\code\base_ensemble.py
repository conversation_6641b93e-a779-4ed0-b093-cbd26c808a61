"""
基础集成学习框架
定义统一的集成学习接口，减少代码重复
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple, Union
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseEnsembleLearner(ABC):
    """集成学习基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化基础集成学习器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.datasets: Dict[str, pd.DataFrame] = {}
        self.trained_models: Dict[str, Any] = {}
        self.ensemble_models: Dict[str, Any] = {}
        self.results: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {
            'created_time': datetime.now().isoformat(),
            'class_name': self.__class__.__name__
        }
        
        # 设置日志
        self.logger = logger
        
    @abstractmethod
    def load_data(self, data_config: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """
        加载数据
        
        Args:
            data_config: 数据配置
            
        Returns:
            Dict[str, pd.DataFrame]: 加载的数据集
        """
        pass
    
    @abstractmethod
    def preprocess_data(self, datasets: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        预处理数据
        
        Args:
            datasets: 原始数据集
            
        Returns:
            Dict[str, pd.DataFrame]: 预处理后的数据集
        """
        pass
    
    @abstractmethod
    def train_base_models(self, model_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        训练基础模型
        
        Args:
            model_config: 模型配置
            
        Returns:
            Dict[str, Any]: 训练好的基础模型
        """
        pass
    
    @abstractmethod
    def create_ensemble(self, fusion_methods: List[str]) -> Dict[str, Any]:
        """
        创建集成模型
        
        Args:
            fusion_methods: 融合方法列表
            
        Returns:
            Dict[str, Any]: 集成模型
        """
        pass
    
    @abstractmethod
    def evaluate(self, test_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        评估模型
        
        Args:
            test_data: 测试数据
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        pass
    
    @abstractmethod
    def generate_shap_analysis(self, output_dir: str) -> Dict[str, Any]:
        """
        生成SHAP分析
        
        Args:
            output_dir: 输出目录
            
        Returns:
            Dict[str, Any]: SHAP分析结果
        """
        pass
    
    @abstractmethod
    def visualize_results(self, output_dir: str) -> bool:
        """
        可视化结果
        
        Args:
            output_dir: 输出目录
            
        Returns:
            bool: 是否成功
        """
        pass
    
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        return [
            'RandomForest', 'LogisticRegression', 'SVM',
            'XGBoost', 'LightGBM', 'NaiveBayes'
        ]
    
    def get_supported_ensemble_methods(self) -> List[str]:
        """
        获取支持的集成方法列表
        
        Returns:
            List[str]: 支持的集成方法名称列表
        """
        return [
            'hard_voting', 'soft_voting', 'logit_weighted',
            'confidence_abstain', 'stacking'
        ]
    
    def get_supported_calibration_methods(self) -> List[str]:
        """
        获取支持的校准方法列表
        
        Returns:
            List[str]: 支持的校准方法名称列表
        """
        return ['platt', 'isotonic', 'temperature']
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置有效性
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置项
            required_keys = ['model_data_mapping']
            for key in required_keys:
                if key not in config:
                    self.logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 检查模型数据映射
            model_data_mapping = config['model_data_mapping']
            if not isinstance(model_data_mapping, dict) or len(model_data_mapping) == 0:
                self.logger.error("model_data_mapping 必须是非空字典")
                return False
            
            # 检查数据路径
            for model_name, data_path in model_data_mapping.items():
                if not Path(data_path).exists():
                    self.logger.error(f"数据文件不存在: {data_path}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def save_results(self, output_dir: str, results: Dict[str, Any]) -> bool:
        """
        保存结果到文件
        
        Args:
            output_dir: 输出目录
            results: 结果字典
            
        Returns:
            bool: 是否保存成功
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 保存结果摘要
            summary_file = output_path / 'results_summary.json'
            import json
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"结果已保存到: {summary_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            return False
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """
        获取管道执行状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'datasets_loaded': len(self.datasets) > 0,
            'models_trained': len(self.trained_models) > 0,
            'ensembles_created': len(self.ensemble_models) > 0,
            'results_available': len(self.results) > 0,
            'dataset_count': len(self.datasets),
            'model_count': len(self.trained_models),
            'ensemble_count': len(self.ensemble_models),
            'metadata': self.metadata
        }
    
    def run_pipeline(self, config: Dict[str, Any], output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        运行完整的集成学习管道
        
        Args:
            config: 配置字典
            output_dir: 输出目录
            
        Returns:
            Dict[str, Any]: 管道执行结果
        """
        try:
            # 验证配置
            if not self.validate_config(config):
                return {'status': 'failed', 'error': '配置验证失败'}
            
            self.config = config
            
            # 步骤1: 加载数据
            self.logger.info("步骤1: 加载数据")
            self.datasets = self.load_data(config)
            if not self.datasets:
                return {'status': 'failed', 'error': '数据加载失败'}
            
            # 步骤2: 预处理数据
            self.logger.info("步骤2: 预处理数据")
            self.datasets = self.preprocess_data(self.datasets)
            
            # 步骤3: 训练基础模型
            self.logger.info("步骤3: 训练基础模型")
            self.trained_models = self.train_base_models(config)
            if not self.trained_models:
                return {'status': 'failed', 'error': '基础模型训练失败'}
            
            # 步骤4: 创建集成模型
            self.logger.info("步骤4: 创建集成模型")
            fusion_methods = config.get('fusion_methods', ['soft_voting'])
            self.ensemble_models = self.create_ensemble(fusion_methods)
            if not self.ensemble_models:
                return {'status': 'failed', 'error': '集成模型创建失败'}
            
            # 步骤5: 评估模型
            self.logger.info("步骤5: 评估模型")
            self.results = self.evaluate(config.get('test_data'))
            
            # 步骤6: 生成SHAP分析（如果启用）
            if config.get('enable_shap', False) and output_dir:
                self.logger.info("步骤6: 生成SHAP分析")
                shap_results = self.generate_shap_analysis(output_dir)
                self.results['shap_analysis'] = shap_results
            
            # 步骤7: 可视化结果（如果指定输出目录）
            if output_dir:
                self.logger.info("步骤7: 可视化结果")
                self.visualize_results(output_dir)
                self.save_results(output_dir, self.results)
            
            # 更新元数据
            self.metadata['completed_time'] = datetime.now().isoformat()
            self.metadata['status'] = 'success'
            
            return {
                'status': 'success',
                'results': self.results,
                'metadata': self.metadata,
                'output_dir': output_dir
            }
            
        except Exception as e:
            self.logger.error(f"管道执行失败: {e}")
            return {'status': 'failed', 'error': str(e)}


class EnsembleMetrics:
    """集成学习评估指标工具类"""
    
    @staticmethod
    def calculate_classification_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                                       y_prob: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        计算分类指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_prob: 预测概率
            
        Returns:
            Dict[str, float]: 评估指标
        """
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        from sklearn.metrics import roc_auc_score, log_loss
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }
        
        if y_prob is not None:
            try:
                if len(np.unique(y_true)) == 2:  # 二分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob[:, 1] if y_prob.ndim > 1 else y_prob)
                else:  # 多分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob, multi_class='ovr', average='weighted')
                
                metrics['log_loss'] = log_loss(y_true, y_prob)
            except Exception as e:
                logger.warning(f"计算AUC或log_loss失败: {e}")
        
        return metrics
