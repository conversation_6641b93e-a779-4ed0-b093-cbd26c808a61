================================================================================
多样性分析综合报告
================================================================================
生成时间: 2025-09-05 01:23:05
分析模型数量: 9
模型列表: DecisionTree, RandomForest, XGBoost, LightGBM, CatBoost, Logistic, SVM, NaiveBayes, NeuralNet
样本数量: 40

1. 模型个体性能概览
----------------------------------------
  DecisionTree: 0.8461
  RandomForest: 0.8257
  XGBoost: 0.7508
  LightGBM: 0.7727
  CatBoost: 0.7963
  Logistic: 0.8812
  SVM: 0.8276
  NaiveBayes: 0.9110
  NeuralNet: 0.8581

2. 多样性指标统计
----------------------------------------
  Q统计量 (平均绝对值): 0.8090 ± 0.3114
  不一致性度量 (平均): 0.1472 ± 0.0953
  双错度量 (平均): 0.1153 ± 0.0596
  相关系数 (平均绝对值): 0.7021 ± 0.1877

3. 多样性质量评估
----------------------------------------
  整体多样性水平: 中等 (0.3380)

4. 建议
----------------------------------------
- Q统计量过高，模型间相关性较强，建议选择更多样化的算法
  - 不一致性度量较低，模型预测过于相似，建议增加算法多样性
  - 相关系数过高，模型预测高度相关，建议选择不同类型的算法

================================================================================