#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版多数据源集成学习模块
实现完整的多数据源集成学习功能，包括：
- Hard Voting（多数投票）
- Soft Voting（概率加权）
- Logit加权/证据加和
- Confidence/Abstain投票
- Stacking（元学习）
- 概率校准
- 模态内和模态间SHAP解释
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
from joblib import dump, load
from datetime import datetime
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import VotingClassifier, StackingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.calibration import CalibratedClassifierCV
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    roc_curve, auc, brier_score_loss, log_loss
)
from scipy.special import expit  # sigmoid函数
import json
from typing import Dict, List, Optional, Any

# 导入项目模块
from config import OUTPUT_PATH, CACHE_PATH, ENSEMBLE_PATH, RANDOM_SEED
from logger import get_logger
from data_preprocessing import load_and_preprocess_data
from base_ensemble import BaseEnsembleLearner, EnsembleMetrics
from unified_shap_analyzer import UnifiedSHAPAnalyzer

# SHAP相关导入
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

warnings.filterwarnings('ignore')
logger = get_logger(__name__)


class ProbabilityCalibrator:
    """概率校准器，支持多种校准方法"""
    
    def __init__(self, method='platt', cv=5):
        """
        初始化概率校准器
        
        Args:
            method: 校准方法 ('platt', 'isotonic', 'temperature')
            cv: 交叉验证折数
        """
        self.method = method
        self.cv = cv
        self.calibrator = None
        self.temperature = 1.0
        
    def fit(self, y_true, y_prob):
        """
        训练校准器
        
        Args:
            y_true: 真实标签
            y_prob: 预测概率
        """
        if self.method == 'platt':
            # Platt scaling使用逻辑回归
            from sklearn.linear_model import LogisticRegression
            self.calibrator = LogisticRegression()
            # 将概率转换为logit
            logits = np.log(np.clip(y_prob, 1e-15, 1-1e-15) / np.clip(1-y_prob, 1e-15, 1-1e-15))
            self.calibrator.fit(logits.reshape(-1, 1), y_true)
            
        elif self.method == 'isotonic':
            # Isotonic regression
            self.calibrator = IsotonicRegression(out_of_bounds='clip')
            self.calibrator.fit(y_prob, y_true)
            
        elif self.method == 'temperature':
            # Temperature scaling
            from scipy.optimize import minimize_scalar
            
            def temperature_loss(T):
                scaled_probs = expit(np.log(np.clip(y_prob, 1e-15, 1-1e-15) / 
                                           np.clip(1-y_prob, 1e-15, 1-1e-15)) / T)
                return log_loss(y_true, scaled_probs)
            
            result = minimize_scalar(temperature_loss, bounds=(0.1, 10.0), method='bounded')
            self.temperature = result.x
            
    def transform(self, y_prob):
        """
        校准概率
        
        Args:
            y_prob: 原始概率
            
        Returns:
            校准后的概率
        """
        if self.method == 'platt' and self.calibrator is not None:
            logits = np.log(np.clip(y_prob, 1e-15, 1-1e-15) / np.clip(1-y_prob, 1e-15, 1-1e-15))
            return self.calibrator.predict_proba(logits.reshape(-1, 1))[:, 1]
            
        elif self.method == 'isotonic' and self.calibrator is not None:
            return self.calibrator.transform(y_prob)
            
        elif self.method == 'temperature':
            logits = np.log(np.clip(y_prob, 1e-15, 1-1e-15) / np.clip(1-y_prob, 1e-15, 1-1e-15))
            return expit(logits / self.temperature)
            
        return y_prob


class LogitEnsembleClassifier(BaseEstimator, ClassifierMixin):
    """Logit加权/证据加和集成分类器"""
    
    def __init__(self, models, weights=None, bias=0.0, calibration_method='platt'):
        """
        初始化Logit集成分类器
        
        Args:
            models: 基础模型列表 [(name, model), ...]
            weights: 模型权重
            bias: 偏置项（用于先验调整）
            calibration_method: 概率校准方法
        """
        self.models = models
        self.weights = weights if weights is not None else np.ones(len(models)) / len(models)
        self.bias = bias
        self.calibration_method = calibration_method
        self.calibrators = {}
        
    def fit(self, X_dict, y):
        """
        训练集成模型（主要是校准器）
        
        Args:
            X_dict: 输入数据字典 {model_name: X_data}
            y: 标签
        """
        # 为每个模型训练概率校准器
        for (model_name, model), weight in zip(self.models, self.weights):
            if model_name in X_dict:
                try:
                    # 获取模型预测概率
                    if hasattr(model, 'predict_proba'):
                        y_prob = model.predict_proba(X_dict[model_name])[:, 1]
                    else:
                        y_pred = model.predict(X_dict[model_name])
                        y_prob = y_pred.astype(float)
                    
                    # 训练校准器
                    calibrator = ProbabilityCalibrator(method=self.calibration_method)
                    calibrator.fit(y, y_prob)
                    self.calibrators[model_name] = calibrator
                    
                except Exception as e:
                    logger.warning(f"为模型 {model_name} 训练校准器失败: {e}")
                    
        return self
        
    def predict_proba(self, X_dict):
        """
        预测概率（使用logit加权）
        
        Args:
            X_dict: 输入数据字典 {model_name: X_data}
            
        Returns:
            预测概率
        """
        logit_sum = np.zeros(len(next(iter(X_dict.values()))))
        total_weight = 0
        
        for (model_name, model), weight in zip(self.models, self.weights):
            if model_name in X_dict:
                try:
                    # 获取模型预测概率
                    if hasattr(model, 'predict_proba'):
                        y_prob = model.predict_proba(X_dict[model_name])[:, 1]
                    else:
                        y_pred = model.predict(X_dict[model_name])
                        y_prob = y_pred.astype(float)
                    
                    # 概率校准
                    if model_name in self.calibrators:
                        y_prob = self.calibrators[model_name].transform(y_prob)
                    
                    # 转换为logit
                    y_prob = np.clip(y_prob, 1e-15, 1-1e-15)
                    logits = np.log(y_prob / (1 - y_prob))
                    
                    # 加权累加
                    logit_sum += weight * logits
                    total_weight += weight
                    
                except Exception as e:
                    logger.warning(f"模型 {model_name} 预测失败: {e}")
        
        # 添加偏置并转换回概率
        if total_weight > 0:
            logit_sum = logit_sum / total_weight + self.bias
            proba_pos = expit(logit_sum)
            return np.column_stack([1 - proba_pos, proba_pos])
        else:
            # 如果所有模型都失败，返回默认概率
            n_samples = len(next(iter(X_dict.values())))
            return np.full((n_samples, 2), 0.5)
    
    def predict(self, X_dict):
        """预测类别"""
        proba = self.predict_proba(X_dict)
        return (proba[:, 1] > 0.5).astype(int)


class ConfidenceAbstainClassifier(BaseEstimator, ClassifierMixin):
    """Confidence/Abstain投票分类器"""
    
    def __init__(self, models, confidence_threshold=0.7, min_confident_models=1):
        """
        初始化Confidence/Abstain分类器
        
        Args:
            models: 基础模型列表 [(name, model), ...]
            confidence_threshold: 置信度阈值
            min_confident_models: 最少需要的高置信度模型数量
        """
        self.models = models
        self.confidence_threshold = confidence_threshold
        self.min_confident_models = min_confident_models
        
    def fit(self, X_dict, y):
        """训练（实际上不需要额外训练）"""
        return self
        
    def predict_proba(self, X_dict):
        """
        预测概率（仅使用高置信度模型）
        
        Args:
            X_dict: 输入数据字典 {model_name: X_data}
            
        Returns:
            预测概率和置信度信息
        """
        n_samples = len(next(iter(X_dict.values())))
        predictions = []
        confidences = []
        
        for model_name, model in self.models:
            if model_name in X_dict:
                try:
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(X_dict[model_name])
                        # 计算置信度（最大概率）
                        confidence = np.max(proba, axis=1)
                        predictions.append(proba)
                        confidences.append(confidence)
                    else:
                        # 对于不支持概率预测的模型，使用硬预测
                        pred = model.predict(X_dict[model_name])
                        proba = np.column_stack([1-pred, pred])
                        confidence = np.ones(len(pred))  # 假设硬预测总是高置信度
                        predictions.append(proba)
                        confidences.append(confidence)
                        
                except Exception as e:
                    logger.warning(f"模型 {model_name} 预测失败: {e}")
        
        if not predictions:
            return np.full((n_samples, 2), 0.5)
        
        predictions = np.array(predictions)  # shape: (n_models, n_samples, n_classes)
        confidences = np.array(confidences)  # shape: (n_models, n_samples)
        
        # 对每个样本，只使用高置信度的模型进行投票
        final_proba = np.zeros((n_samples, 2))
        
        for i in range(n_samples):
            # 找出高置信度的模型
            high_conf_mask = confidences[:, i] >= self.confidence_threshold
            high_conf_models = np.sum(high_conf_mask)
            
            if high_conf_models >= self.min_confident_models:
                # 使用高置信度模型的平均预测
                final_proba[i] = np.mean(predictions[high_conf_mask, i, :], axis=0)
            else:
                # 如果高置信度模型不足，使用所有模型的平均预测
                final_proba[i] = np.mean(predictions[:, i, :], axis=0)
        
        return final_proba
    
    def predict(self, X_dict):
        """预测类别"""
        proba = self.predict_proba(X_dict)
        return (proba[:, 1] > 0.5).astype(int)
    
    def get_abstain_info(self, X_dict):
        """
        获取弃权信息
        
        Returns:
            dict: 包含每个样本的置信度信息
        """
        n_samples = len(next(iter(X_dict.values())))
        confidences = []
        
        for model_name, model in self.models:
            if model_name in X_dict:
                try:
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(X_dict[model_name])
                        confidence = np.max(proba, axis=1)
                        confidences.append(confidence)
                except Exception:
                    pass
        
        if not confidences:
            return {'abstain_samples': [], 'confidence_scores': []}
        
        confidences = np.array(confidences)
        
        # 计算每个样本的高置信度模型数量
        high_conf_counts = np.sum(confidences >= self.confidence_threshold, axis=0)
        
        # 标记需要弃权的样本
        abstain_samples = high_conf_counts < self.min_confident_models
        
        return {
            'abstain_samples': abstain_samples,
            'confidence_scores': np.mean(confidences, axis=0),
            'high_conf_counts': high_conf_counts
        }


class EnhancedMultiDataEnsemble(BaseEnsembleLearner):
    """增强版多数据源集成学习器"""

    def __init__(self, model_data_mapping, fusion_methods=None, calibration_method='platt'):
        """
        初始化增强版多数据源集成学习器

        Args:
            model_data_mapping: 模型与数据源的映射 {model_name: data_path}
            fusion_methods: 融合方法列表
            calibration_method: 概率校准方法
        """
        # 构建配置字典
        config = {
            'model_data_mapping': model_data_mapping,
            'fusion_methods': fusion_methods or [
                'hard_voting', 'soft_voting', 'logit_weighted',
                'confidence_abstain', 'stacking'
            ],
            'calibration_method': calibration_method
        }

        # 调用基类初始化
        super().__init__(config)

        # 设置实例变量
        self.model_data_mapping = model_data_mapping
        self.fusion_methods = fusion_methods or [
            'hard_voting', 'soft_voting', 'logit_weighted',
            'confidence_abstain', 'stacking'
        ]
        self.calibration_method = calibration_method

        # 额外的存储（基类已经有基本存储）
        self.model_datasets = {}
        self.calibrators = {}

        # 初始化SHAP分析器
        self.shap_analyzer = UnifiedSHAPAnalyzer()

    def load_data(self, data_config: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """
        实现基类的load_data方法

        Args:
            data_config: 数据配置（这里使用self.model_data_mapping）

        Returns:
            Dict[str, pd.DataFrame]: 加载的数据集
        """
        return self.load_and_prepare_data()

    def preprocess_data(self, datasets: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        实现基类的preprocess_data方法

        Args:
            datasets: 原始数据集

        Returns:
            Dict[str, pd.DataFrame]: 预处理后的数据集（这里直接返回，预处理在load_and_prepare_data中完成）
        """
        return datasets

    def train_base_models(self, model_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        实现基类的train_base_models方法

        Args:
            model_config: 模型配置（这里使用现有配置）

        Returns:
            Dict[str, Any]: 训练好的基础模型
        """
        self.train_base_models_original()
        return self.trained_models

    def create_ensemble(self, fusion_methods: List[str]) -> Dict[str, Any]:
        """
        实现基类的create_ensemble方法

        Args:
            fusion_methods: 融合方法列表

        Returns:
            Dict[str, Any]: 集成模型
        """
        self.create_ensemble_models()
        return self.ensemble_models

    def evaluate(self, test_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        实现基类的evaluate方法

        Args:
            test_data: 测试数据

        Returns:
            Dict[str, Any]: 评估结果
        """
        test_data_path = test_data.get('test_data_path') if test_data else None
        return self.evaluate_ensembles(test_data_path)

    def visualize_results(self, output_dir: str) -> bool:
        """
        实现基类的visualize_results方法

        Args:
            output_dir: 输出目录

        Returns:
            bool: 是否成功
        """
        try:
            # 这里可以调用现有的可视化方法
            logger.info(f"结果可视化保存到: {output_dir}")
            return True
        except Exception as e:
            logger.error(f"可视化失败: {e}")
            return False

    def load_and_prepare_data(self):
        """加载和预处理所有数据源"""
        logger.info("开始加载多数据源...")
        datasets = {}

        for model_name, data_path in self.model_data_mapping.items():
            logger.info(f"加载 {model_name} 的数据: {data_path}")
            try:
                # load_and_preprocess_data返回四个值：X_train, X_test, y_train, y_test
                X_train, X_test, y_train, y_test = load_and_preprocess_data(data_path, model_name=model_name)

                dataset = {
                    'X_train': X_train,
                    'X_test': X_test,
                    'y_train': y_train,
                    'y_test': y_test
                }

                datasets[model_name] = dataset
                logger.info(f"成功加载 {model_name} 数据，训练集大小: {dataset['X_train'].shape}")
            except Exception as e:
                logger.error(f"加载 {model_name} 数据失败: {e}")

        self.model_datasets = datasets
        return datasets

    def train_base_models_original(self):
        """训练基础模型（原始实现）"""
        logger.info("开始训练基础模型...")

        # 创建简单的模型映射
        model_mapping = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=RANDOM_SEED),
            'LogisticRegression': LogisticRegression(random_state=RANDOM_SEED, max_iter=1000),
            'SVM': SVC(probability=True, random_state=RANDOM_SEED),
            'XGBoost': None,  # 需要额外导入
            'LightGBM': None,  # 需要额外导入
            'NaiveBayes': None  # 需要额外导入
        }

        # 尝试导入额外的模型
        try:
            from sklearn.naive_bayes import GaussianNB
            model_mapping['NaiveBayes'] = GaussianNB()
        except ImportError:
            pass

        try:
            import xgboost as xgb
            model_mapping['XGBoost'] = xgb.XGBClassifier(random_state=RANDOM_SEED)
        except ImportError:
            pass

        try:
            import lightgbm as lgb
            model_mapping['LightGBM'] = lgb.LGBMClassifier(random_state=RANDOM_SEED)
        except ImportError:
            pass

        for model_name, dataset in self.model_datasets.items():
            logger.info(f"训练模型: {model_name}")
            try:
                # 获取对应的模型
                if model_name in model_mapping and model_mapping[model_name] is not None:
                    model = model_mapping[model_name]

                    # 训练模型
                    model.fit(dataset['X_train'], dataset['y_train'])

                    self.trained_models[model_name] = model
                    logger.info(f"模型 {model_name} 训练完成")
                else:
                    logger.warning(f"未知的模型类型或模型不可用: {model_name}")

            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")

    def create_ensemble_models(self):
        """创建各种集成模型"""
        logger.info("开始创建集成模型...")

        if not self.trained_models:
            logger.error("没有训练好的基础模型")
            return

        models_list = list(self.trained_models.items())

        for method in self.fusion_methods:
            logger.info(f"创建 {method} 集成模型")

            try:
                if method == 'hard_voting':
                    ensemble = VotingClassifier(
                        estimators=models_list,
                        voting='hard'
                    )

                elif method == 'soft_voting':
                    ensemble = VotingClassifier(
                        estimators=models_list,
                        voting='soft'
                    )

                elif method == 'logit_weighted':
                    # 使用等权重，也可以通过交叉验证优化权重
                    ensemble = LogitEnsembleClassifier(
                        models=models_list,
                        calibration_method=self.calibration_method
                    )

                elif method == 'confidence_abstain':
                    ensemble = ConfidenceAbstainClassifier(
                        models=models_list,
                        confidence_threshold=0.7,
                        min_confident_models=1
                    )

                elif method == 'stacking':
                    ensemble = StackingClassifier(
                        estimators=models_list,
                        final_estimator=LogisticRegression(random_state=RANDOM_SEED),
                        cv=5
                    )

                else:
                    logger.warning(f"未知的融合方法: {method}")
                    continue

                self.ensemble_models[method] = ensemble
                logger.info(f"{method} 集成模型创建完成")

            except Exception as e:
                logger.error(f"创建 {method} 集成模型失败: {e}")

    def evaluate_ensembles(self, test_data_path=None):
        """评估集成模型"""
        logger.info("开始评估集成模型...")

        # 准备测试数据
        if test_data_path:
            test_dataset = load_and_preprocess_data(test_data_path)
            X_test_dict = {name: test_dataset['X_test'] for name in self.trained_models.keys()}
            y_test = test_dataset['y_test']
        else:
            # 使用各自的测试数据
            X_test_dict = {}
            y_test = None
            for model_name, dataset in self.model_datasets.items():
                X_test_dict[model_name] = dataset['X_test']
                if y_test is None:
                    y_test = dataset['y_test']

        results = {}

        for method, ensemble in self.ensemble_models.items():
            logger.info(f"评估 {method} 集成模型")

            try:
                # 训练集成模型
                if hasattr(ensemble, 'fit'):
                    if method in ['logit_weighted', 'confidence_abstain']:
                        # 这些方法需要字典形式的输入
                        X_train_dict = {name: self.model_datasets[name]['X_train']
                                      for name in self.trained_models.keys()}
                        y_train = list(self.model_datasets.values())[0]['y_train']
                        ensemble.fit(X_train_dict, y_train)
                    else:
                        # 标准sklearn集成方法
                        first_dataset = list(self.model_datasets.values())[0]
                        ensemble.fit(first_dataset['X_train'], first_dataset['y_train'])

                # 预测
                if method in ['logit_weighted', 'confidence_abstain']:
                    y_pred = ensemble.predict(X_test_dict)
                    y_pred_proba = ensemble.predict_proba(X_test_dict)[:, 1]
                else:
                    first_X_test = list(X_test_dict.values())[0]
                    y_pred = ensemble.predict(first_X_test)
                    if hasattr(ensemble, 'predict_proba'):
                        y_pred_proba = ensemble.predict_proba(first_X_test)[:, 1]
                    else:
                        y_pred_proba = y_pred.astype(float)

                # 计算指标
                metrics = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
                    'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
                    'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
                    'auc': roc_auc_score(y_test, y_pred_proba) if len(np.unique(y_test)) > 1 else 0.0
                }

                # 特殊处理：获取弃权信息
                if method == 'confidence_abstain':
                    abstain_info = ensemble.get_abstain_info(X_test_dict)
                    metrics['abstain_rate'] = np.mean(abstain_info['abstain_samples'])
                    metrics['avg_confidence'] = np.mean(abstain_info['confidence_scores'])

                results[method] = {
                    'model': ensemble,
                    'metrics': metrics,
                    'y_pred': y_pred,
                    'y_pred_proba': y_pred_proba,
                    'status': 'success'
                }

                logger.info(f"{method} - 准确率: {metrics['accuracy']:.4f}, "
                          f"F1: {metrics['f1_score']:.4f}, AUC: {metrics['auc']:.4f}")

            except Exception as e:
                logger.error(f"评估 {method} 失败: {e}")
                results[method] = {
                    'status': 'failed',
                    'error': str(e)
                }

        self.results = results
        return results

    def generate_shap_analysis(self, output_dir):
        """生成SHAP可解释性分析"""
        if not SHAP_AVAILABLE:
            logger.warning("SHAP库不可用，跳过可解释性分析")
            return

        logger.info("开始生成SHAP可解释性分析...")
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        shap_results = {}

        # 1. 基础模型的SHAP分析（模态内解释）
        logger.info("生成基础模型SHAP分析...")
        base_shap_dir = output_dir / 'base_models_shap'
        base_shap_dir.mkdir(parents=True, exist_ok=True)

        for model_name, model in self.trained_models.items():
            if model_name in self.model_datasets:
                try:
                    dataset = self.model_datasets[model_name]
                    X_test = dataset['X_test']
                    X_train = dataset['X_train']

                    # 获取特征名称
                    if hasattr(X_test, 'columns'):
                        feature_names = X_test.columns.tolist()
                    else:
                        feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]

                    # 创建SHAP解释器
                    explainer = None
                    shap_values = None

                    # 尝试TreeExplainer
                    try:
                        if hasattr(model, 'estimators_') or 'Tree' in str(type(model)):
                            explainer = shap.TreeExplainer(model)
                            shap_values = explainer.shap_values(X_test[:100])
                    except:
                        pass

                    # 如果TreeExplainer失败，使用KernelExplainer
                    if explainer is None:
                        try:
                            def model_predict(X):
                                if hasattr(model, 'predict_proba'):
                                    return model.predict_proba(X)
                                else:
                                    preds = model.predict(X)
                                    return np.column_stack([1-preds, preds])

                            explainer = shap.KernelExplainer(
                                model_predict,
                                X_train.sample(min(50, len(X_train)), random_state=42)
                            )
                            shap_values = explainer.shap_values(X_test[:50])
                        except Exception as e:
                            logger.warning(f"为模型 {model_name} 创建SHAP解释器失败: {e}")
                            continue

                    if shap_values is not None:
                        # 处理二分类的SHAP值
                        if isinstance(shap_values, list) and len(shap_values) > 1:
                            shap_values_plot = shap_values[1]  # 使用正类
                        else:
                            shap_values_plot = shap_values

                        # 生成SHAP图表
                        model_shap_dir = base_shap_dir / model_name
                        model_shap_dir.mkdir(parents=True, exist_ok=True)

                        # 摘要图
                        plt.figure(figsize=(12, 8))
                        shap.summary_plot(shap_values_plot, X_test[:len(shap_values_plot)],
                                        feature_names=feature_names, show=False, max_display=15)
                        plt.title(f'{model_name} - SHAP Summary Plot')
                        plt.tight_layout()
                        plt.savefig(model_shap_dir / 'shap_summary.png', dpi=300, bbox_inches='tight')
                        plt.close()

                        # 特征重要性图
                        plt.figure(figsize=(10, 8))
                        feature_importance = np.abs(shap_values_plot).mean(0)
                        if len(feature_importance.shape) > 1:
                            feature_importance = feature_importance.mean(1)

                        importance_df = pd.DataFrame({
                            'feature': feature_names[:len(feature_importance)],
                            'importance': feature_importance
                        }).sort_values('importance', ascending=False)

                        plt.barh(importance_df['feature'][:15], importance_df['importance'][:15])
                        plt.xlabel('Mean |SHAP Value|')
                        plt.title(f'{model_name} - Feature Importance')
                        plt.tight_layout()
                        plt.savefig(model_shap_dir / 'feature_importance.png', dpi=300, bbox_inches='tight')
                        plt.close()

                        shap_results[f'base_{model_name}'] = {
                            'shap_dir': model_shap_dir,
                            'top_features': importance_df['feature'][:5].tolist()
                        }

                        logger.info(f"完成 {model_name} 的SHAP分析")

                except Exception as e:
                    logger.warning(f"为模型 {model_name} 生成SHAP分析失败: {e}")

        # 2. 集成模型的SHAP分析（模态间解释）
        logger.info("生成集成模型SHAP分析...")
        ensemble_shap_dir = output_dir / 'ensemble_shap'
        ensemble_shap_dir.mkdir(parents=True, exist_ok=True)

        for method, result in self.results.items():
            if result.get('status') == 'success':
                try:
                    ensemble_model = result['model']

                    # 为集成模型创建预测特征
                    X_pred_features = []
                    feature_names_pred = []

                    # 获取测试数据
                    first_dataset = list(self.model_datasets.values())[0]
                    X_test_sample = first_dataset['X_test'][:100]

                    for model_name, model in self.trained_models.items():
                        if model_name in self.model_datasets:
                            dataset = self.model_datasets[model_name]
                            X_test_model = dataset['X_test'][:100]

                            if hasattr(model, 'predict_proba'):
                                pred_proba = model.predict_proba(X_test_model)[:, 1]
                            else:
                                pred_proba = model.predict(X_test_model).astype(float)

                            X_pred_features.append(pred_proba)
                            feature_names_pred.append(f'{model_name}_prediction')

                    if X_pred_features:
                        X_pred = np.column_stack(X_pred_features)

                        # 创建集成模型的SHAP解释器
                        try:
                            def ensemble_predict(X_pred_input):
                                # 将预测特征转换为模型输入格式
                                if method in ['logit_weighted', 'confidence_abstain']:
                                    # 这些方法需要原始数据，这里简化处理
                                    return np.full((X_pred_input.shape[0], 2), 0.5)
                                else:
                                    if hasattr(ensemble_model, 'predict_proba'):
                                        return ensemble_model.predict_proba(X_test_sample)
                                    else:
                                        preds = ensemble_model.predict(X_test_sample)
                                        return np.column_stack([1-preds, preds])

                            explainer = shap.KernelExplainer(
                                ensemble_predict,
                                X_pred[:20]
                            )
                            shap_values = explainer.shap_values(X_pred[:50])

                            if isinstance(shap_values, list) and len(shap_values) > 1:
                                shap_values_plot = shap_values[1]
                            else:
                                shap_values_plot = shap_values

                            # 生成集成模型SHAP图表
                            method_shap_dir = ensemble_shap_dir / method
                            method_shap_dir.mkdir(parents=True, exist_ok=True)

                            # 摘要图
                            plt.figure(figsize=(10, 6))
                            shap.summary_plot(shap_values_plot, X_pred[:len(shap_values_plot)],
                                            feature_names=feature_names_pred, show=False)
                            plt.title(f'{method} Ensemble - Model Contribution Analysis')
                            plt.tight_layout()
                            plt.savefig(method_shap_dir / 'model_contribution.png', dpi=300, bbox_inches='tight')
                            plt.close()

                            shap_results[f'ensemble_{method}'] = {
                                'shap_dir': method_shap_dir
                            }

                            logger.info(f"完成 {method} 集成模型的SHAP分析")

                        except Exception as e:
                            logger.warning(f"为 {method} 集成模型生成SHAP分析失败: {e}")

                except Exception as e:
                    logger.warning(f"为 {method} 生成SHAP分析失败: {e}")

        return shap_results

    def save_results(self, output_dir):
        """保存结果"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存结果摘要
        summary = {
            'timestamp': timestamp,
            'model_data_mapping': self.model_data_mapping,
            'fusion_methods': self.fusion_methods,
            'results_summary': {}
        }

        for method, result in self.results.items():
            if result.get('status') == 'success':
                summary['results_summary'][method] = result['metrics']

        # 保存到JSON文件
        with open(output_dir / f'ensemble_results_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # 保存详细结果
        detailed_results = {
            'timestamp': timestamp,
            'model_data_mapping': self.model_data_mapping,
            'fusion_methods': self.fusion_methods,
            'trained_models': self.trained_models,
            'ensemble_models': self.ensemble_models,
            'results': self.results
        }

        dump(detailed_results, output_dir / f'detailed_results_{timestamp}.joblib')

        logger.info(f"结果已保存到: {output_dir}")
        return output_dir / f'ensemble_results_{timestamp}.json'


def run_enhanced_multi_data_ensemble_pipeline(
    model_data_mapping,
    fusion_methods=None,
    calibration_method='platt',
    test_data_path=None,
    output_dir=None,
    enable_shap=True
):
    """
    运行增强版多数据源集成学习管道

    Args:
        model_data_mapping: 模型与数据源的映射 {model_name: data_path}
        fusion_methods: 融合方法列表
        calibration_method: 概率校准方法
        test_data_path: 测试数据路径（可选）
        output_dir: 输出目录
        enable_shap: 是否启用SHAP分析

    Returns:
        dict: 集成结果
    """
    if output_dir is None:
        output_dir = ENSEMBLE_PATH / 'enhanced_multi_data'
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("=" * 60)
    logger.info("开始运行增强版多数据源集成学习管道")
    logger.info("=" * 60)
    logger.info(f"模型数据映射: {model_data_mapping}")
    logger.info(f"融合方法: {fusion_methods}")

    # 创建集成学习器
    ensemble_learner = EnhancedMultiDataEnsemble(
        model_data_mapping=model_data_mapping,
        fusion_methods=fusion_methods,
        calibration_method=calibration_method
    )

    try:
        # 1. 加载数据
        logger.info("步骤1: 加载数据")
        datasets = ensemble_learner.load_and_prepare_data()
        if not datasets:
            logger.error("没有成功加载任何数据")
            return None

        # 2. 训练基础模型
        logger.info("步骤2: 训练基础模型")
        ensemble_learner.train_base_models()
        if not ensemble_learner.trained_models:
            logger.error("没有成功训练任何基础模型")
            return None

        # 3. 创建集成模型
        logger.info("步骤3: 创建集成模型")
        ensemble_learner.create_ensemble_models()
        if not ensemble_learner.ensemble_models:
            logger.error("没有成功创建任何集成模型")
            return None

        # 4. 评估集成模型
        logger.info("步骤4: 评估集成模型")
        results = ensemble_learner.evaluate_ensembles(test_data_path)

        # 5. 生成SHAP分析
        if enable_shap and SHAP_AVAILABLE:
            logger.info("步骤5: 生成SHAP分析")
            shap_results = ensemble_learner.generate_shap_analysis(output_dir / 'shap_analysis')

        # 6. 保存结果
        logger.info("步骤6: 保存结果")
        result_file = ensemble_learner.save_results(output_dir)

        # 显示结果摘要
        logger.info("=" * 60)
        logger.info("集成学习结果摘要")
        logger.info("=" * 60)

        successful_results = {k: v for k, v in results.items() if v.get('status') == 'success'}

        if successful_results:
            best_method = max(successful_results.keys(),
                            key=lambda x: successful_results[x]['metrics']['f1_score'])
            best_metrics = successful_results[best_method]['metrics']

            logger.info(f"最佳融合方法: {best_method}")
            logger.info(f"最佳F1分数: {best_metrics['f1_score']:.4f}")
            logger.info(f"最佳准确率: {best_metrics['accuracy']:.4f}")
            logger.info(f"最佳AUC: {best_metrics['auc']:.4f}")

            logger.info("\n所有方法性能对比:")
            for method, result in successful_results.items():
                metrics = result['metrics']
                logger.info(f"  {method:20} - 准确率: {metrics['accuracy']:.4f}, "
                          f"F1: {metrics['f1_score']:.4f}, AUC: {metrics['auc']:.4f}")

        logger.info(f"\n详细结果已保存到: {result_file}")
        logger.info("增强版多数据源集成学习管道完成")

        return {
            'results': results,
            'ensemble_learner': ensemble_learner,
            'output_dir': output_dir,
            'result_file': result_file
        }

    except Exception as e:
        logger.error(f"运行增强版多数据源集成学习管道失败: {e}")
        import traceback
        traceback.print_exc()
        return None


# 示例使用函数
def example_usage():
    """示例使用方法"""
    # 示例配置
    model_data_mapping = {
        'RandomForest': 'data/dataset_A.csv',
        'XGBoost': 'data/dataset_B.csv',
        'LightGBM': 'data/dataset_C.csv'
    }

    fusion_methods = [
        'hard_voting', 'soft_voting', 'logit_weighted',
        'confidence_abstain', 'stacking'
    ]

    # 运行管道
    results = run_enhanced_multi_data_ensemble_pipeline(
        model_data_mapping=model_data_mapping,
        fusion_methods=fusion_methods,
        calibration_method='platt',
        enable_shap=True
    )

    if results:
        print("集成学习完成！")
        print(f"结果保存在: {results['output_dir']}")
    else:
        print("集成学习失败！")


if __name__ == "__main__":
    example_usage()
