#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模型集成机器学习平台 - 重构版GUI主界面
提供简洁、模块化的图形用户界面
"""

import sys
import os
from pathlib import Path
import logging
import tkinter as tk
from tkinter import messagebox

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gui_refactored.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 导入项目模块
try:
    from config import MODEL_NAMES, MODEL_DISPLAY_NAMES, OUTPUT_PATH, CACHE_PATH
    from logger import get_logger
    logger.info("核心模块导入成功")
except ImportError as e:
    logger.error(f"导入核心模块失败: {e}")
    messagebox.showerror("错误", f"导入核心模块失败: {e}")
    sys.exit(1)

# 复现性：设置全局随机种子
try:
    from config import set_global_seed, RANDOM_SEED
    set_global_seed(RANDOM_SEED)
    logger.info(f"全局随机种子设置为: {RANDOM_SEED}")
except Exception as e:
    logger.warning(f"设置随机种子失败: {e}")

# 导入重构后的GUI模块
try:
    from gui.main_window import MainWindow
    logger.info("重构GUI模块导入成功")
except ImportError as e:
    logger.error(f"导入重构GUI模块失败: {e}")
    # 如果重构模块导入失败，回退到原始GUI
    try:
        from gui_main import MLPlatformGUI as FallbackGUI
        logger.warning("使用原始GUI作为回退方案")
        
        class MainWindow:
            def __init__(self):
                self.app = FallbackGUI()
            
            def run(self):
                self.app.run()
                
    except ImportError as e2:
        logger.error(f"回退GUI也导入失败: {e2}")
        messagebox.showerror("错误", "无法导入GUI模块，程序无法启动")
        sys.exit(1)


class RefactoredMLPlatform:
    """重构版机器学习平台主类"""
    
    def __init__(self):
        """初始化平台"""
        self.logger = get_logger("RefactoredPlatform")
        self.logger.info("初始化重构版机器学习平台...")
        
        # 检查环境
        self._check_environment()
        
        # 创建主窗口
        try:
            self.main_window = MainWindow()
            self.logger.info("主窗口创建成功")
        except Exception as e:
            self.logger.error(f"主窗口创建失败: {e}")
            raise
    
    def _check_environment(self):
        """检查运行环境"""
        # 检查Python版本
        if sys.version_info < (3, 7):
            raise RuntimeError("需要Python 3.7或更高版本")
        
        # 检查必要的目录
        required_dirs = [OUTPUT_PATH, CACHE_PATH]
        for dir_path in required_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"确保目录存在: {dir_path}")
        
        # 检查必要的模块
        required_modules = [
            'pandas', 'numpy', 'scikit-learn', 'matplotlib', 'seaborn'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            error_msg = f"缺少必要的模块: {', '.join(missing_modules)}"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
        
        self.logger.info("环境检查通过")
    
    def run(self):
        """运行平台"""
        try:
            self.logger.info("启动重构版机器学习平台")
            self.main_window.run()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行出错: {e}")
            messagebox.showerror("错误", f"程序运行出错: {str(e)}")
        finally:
            self.logger.info("程序结束")


def show_startup_info():
    """显示启动信息"""
    startup_info = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                多模型集成机器学习平台 v2.0                    ║
    ║                    重构版 - 模块化架构                       ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  特性:                                                       ║
    ║  • 模块化设计，代码结构清晰                                   ║
    ║  • 事件驱动架构，组件解耦                                     ║
    ║  • 统一的配置管理                                           ║
    ║  • 增强的错误处理和恢复                                       ║
    ║  • 性能优化和内存管理                                         ║
    ║  • 完整的GUI功能保持不变                                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(startup_info)


def main():
    """主函数"""
    try:
        # 显示启动信息
        show_startup_info()
        
        # 创建并运行平台
        platform = RefactoredMLPlatform()
        platform.run()
        
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        messagebox.showerror("启动失败", f"程序启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
