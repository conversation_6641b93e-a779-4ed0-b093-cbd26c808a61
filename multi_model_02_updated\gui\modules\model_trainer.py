"""
模型训练模块
处理模型选择、训练、超参数调优等功能
"""

import tkinter as tk
from tkinter import ttk
import threading
from typing import Dict, List, Optional, Any
import logging

from ..core.base_gui import BaseGUI
from ..core.event_manager import EventType

logger = logging.getLogger(__name__)


class ModelTrainer(BaseGUI):
    """模型训练器"""
    
    def __init__(self, parent: tk.Widget):
        """
        初始化模型训练器
        
        Args:
            parent: 父组件
        """
        # 模型配置
        self.available_models = {
            "RandomForest": "随机森林",
            "XGBoost": "XGBoost",
            "LightGBM": "LightGBM", 
            "LogisticRegression": "逻辑回归",
            "SVM": "支持向量机",
            "NaiveBayes": "朴素贝叶斯",
            "KNN": "K近邻",
            "DecisionTree": "决策树"
        }
        
        # 模型选择状态
        self.model_vars: Dict[str, tk.BooleanVar] = {}
        self.training_progress = tk.DoubleVar()
        self.is_training = False
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置模型训练UI"""
        self.main_frame = self.factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建模型选择区域
        self._create_model_selection_section()
        
        # 创建训练参数区域
        self._create_training_params_section()
        
        # 创建训练控制区域
        self._create_training_control_section()
        
        # 创建训练状态区域
        self._create_training_status_section()
    
    def _create_model_selection_section(self):
        """创建模型选择区域"""
        selection_frame = self.factory.create_label_frame(self.main_frame, text="模型选择")
        selection_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 创建模型复选框
        models_frame = self.factory.create_frame(selection_frame)
        models_frame.pack(fill=tk.X, padx=5, pady=5)
        
        row, col = 0, 0
        for model_key, model_name in self.available_models.items():
            var = tk.BooleanVar()
            self.model_vars[model_key] = var
            
            # 绑定变化事件
            var.trace_add('write', self._on_model_selection_changed)
            
            cb = self.factory.create_checkbutton(
                models_frame, text=model_name, variable=var
            )
            cb.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
            
            col += 1
            if col >= 3:  # 每行3个
                col = 0
                row += 1
        
        # 快速选择按钮
        quick_select_frame = self.factory.create_frame(selection_frame)
        quick_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        buttons = [
            {"text": "全选", "command": self._select_all_models},
            {"text": "全不选", "command": self._deselect_all_models},
            {"text": "推荐组合", "command": self._select_recommended_models},
            {"text": "快速模型", "command": self._select_fast_models}
        ]
        
        self.create_button_group(quick_select_frame, buttons)
    
    def _create_training_params_section(self):
        """创建训练参数区域"""
        params_frame = self.factory.create_label_frame(self.main_frame, text="训练参数")
        params_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 创建参数输入区域
        params_grid = self.factory.create_frame(params_frame)
        params_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # 交叉验证折数
        self.cv_folds = tk.IntVar(value=5)
        cv_label, cv_entry = self.create_form_field(
            params_grid, "交叉验证折数:", "entry", textvariable=self.cv_folds, width=10
        )
        cv_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        cv_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 测试集比例
        self.test_size = tk.DoubleVar(value=0.2)
        test_label, test_entry = self.create_form_field(
            params_grid, "测试集比例:", "entry", textvariable=self.test_size, width=10
        )
        test_label.grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        test_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        
        # 随机种子
        self.random_state = tk.IntVar(value=42)
        seed_label, seed_entry = self.create_form_field(
            params_grid, "随机种子:", "entry", textvariable=self.random_state, width=10
        )
        seed_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        seed_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 评估指标
        self.scoring_metric = tk.StringVar(value="accuracy")
        metric_label, metric_combo = self.create_form_field(
            params_grid, "评估指标:", "combobox", 
            textvariable=self.scoring_metric,
            values=["accuracy", "precision", "recall", "f1", "roc_auc"],
            width=15
        )
        metric_label.grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        metric_combo.grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
        
        # 高级选项
        advanced_frame = self.factory.create_frame(params_frame)
        advanced_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.enable_feature_selection = tk.BooleanVar(value=False)
        self.enable_hyperparameter_tuning = tk.BooleanVar(value=False)
        self.enable_early_stopping = tk.BooleanVar(value=True)
        
        self.factory.create_checkbutton(
            advanced_frame, text="启用特征选择", variable=self.enable_feature_selection
        ).pack(side=tk.LEFT, padx=5)
        
        self.factory.create_checkbutton(
            advanced_frame, text="启用超参数调优", variable=self.enable_hyperparameter_tuning
        ).pack(side=tk.LEFT, padx=5)
        
        self.factory.create_checkbutton(
            advanced_frame, text="启用早停", variable=self.enable_early_stopping
        ).pack(side=tk.LEFT, padx=5)
    
    def _create_training_control_section(self):
        """创建训练控制区域"""
        control_frame = self.factory.create_label_frame(self.main_frame, text="训练控制")
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        button_frame = self.factory.create_frame(control_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 训练按钮
        self.train_button = self.factory.create_button(
            button_frame, text="开始训练", command=self._start_training,
            style="Primary.TButton"
        )
        self.train_button.pack(side=tk.LEFT, padx=5)
        
        # 停止按钮
        self.stop_button = self.factory.create_button(
            button_frame, text="停止训练", command=self._stop_training,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 重置按钮
        self.factory.create_button(
            button_frame, text="重置参数", command=self._reset_params
        ).pack(side=tk.LEFT, padx=5)
        
        # 保存配置按钮
        self.factory.create_button(
            button_frame, text="保存配置", command=self._save_config
        ).pack(side=tk.LEFT, padx=5)
        
        # 加载配置按钮
        self.factory.create_button(
            button_frame, text="加载配置", command=self._load_config
        ).pack(side=tk.LEFT, padx=5)
    
    def _create_training_status_section(self):
        """创建训练状态区域"""
        status_frame = self.factory.create_label_frame(self.main_frame, text="训练状态")
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # 进度条
        progress_frame = self.factory.create_frame(status_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.factory.create_label(progress_frame, text="训练进度:").pack(side=tk.LEFT)
        
        self.progress_bar = self.factory.create_progressbar(
            progress_frame, variable=self.training_progress, length=300
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        self.progress_label = self.factory.create_label(progress_frame, text="0%")
        self.progress_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 状态日志
        log_frame = self.factory.create_frame(status_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = self.factory.create_text(log_frame, height=8, wrap=tk.WORD)
        
        log_scrollbar = self.factory.create_scrollbar(
            log_frame, orient="vertical", command=self.log_text.yview
        )
        
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _on_model_selection_changed(self, *args):
        """模型选择变化事件处理"""
        selected_models = self.get_selected_models()
        
        # 发布模型选择事件
        self.publish_event(EventType.MODEL_SELECTED, {
            "selected_models": selected_models,
            "count": len(selected_models)
        })
        
        # 更新状态
        if selected_models:
            self.update_status(f"已选择 {len(selected_models)} 个模型")
        else:
            self.update_status("未选择模型")
    
    def _select_all_models(self):
        """选择所有模型"""
        for var in self.model_vars.values():
            var.set(True)
    
    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
    
    def _select_recommended_models(self):
        """选择推荐模型组合"""
        recommended = ["RandomForest", "XGBoost", "LogisticRegression"]
        
        # 先清空所有选择
        self._deselect_all_models()
        
        # 选择推荐模型
        for model in recommended:
            if model in self.model_vars:
                self.model_vars[model].set(True)
    
    def _select_fast_models(self):
        """选择快速训练模型"""
        fast_models = ["LogisticRegression", "NaiveBayes", "KNN"]
        
        # 先清空所有选择
        self._deselect_all_models()
        
        # 选择快速模型
        for model in fast_models:
            if model in self.model_vars:
                self.model_vars[model].set(True)
    
    def _start_training(self):
        """开始训练"""
        selected_models = self.get_selected_models()
        
        if not selected_models:
            self.show_warning("警告", "请至少选择一个模型")
            return
        
        # 检查是否有数据
        # 这里应该从数据管理器获取数据，暂时跳过检查
        
        # 更新UI状态
        self.is_training = True
        self.train_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 在后台线程中开始训练
        training_thread = threading.Thread(
            target=self._training_worker,
            args=(selected_models,),
            daemon=True
        )
        training_thread.start()
        
        self.log_message("开始训练模型...")
        self.update_status("正在训练模型...")
        
        # 发布训练开始事件
        self.publish_event(EventType.MODEL_TRAINING_STARTED, {
            "models": selected_models,
            "params": self._get_training_params()
        })
    
    def _stop_training(self):
        """停止训练"""
        self.is_training = False
        self.train_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.log_message("训练已停止")
        self.update_status("训练已停止")
    
    def _training_worker(self, selected_models: List[str]):
        """训练工作线程"""
        try:
            total_models = len(selected_models)
            
            for i, model_name in enumerate(selected_models):
                if not self.is_training:
                    break
                
                self.log_message(f"正在训练 {self.available_models[model_name]}...")
                
                # 模拟训练过程
                import time
                for j in range(10):
                    if not self.is_training:
                        break
                    time.sleep(0.1)  # 模拟训练时间
                    
                    # 更新进度
                    progress = ((i * 10 + j + 1) / (total_models * 10)) * 100
                    self.training_progress.set(progress)
                    self.progress_label.config(text=f"{progress:.1f}%")
                
                if self.is_training:
                    self.log_message(f"{self.available_models[model_name]} 训练完成")
            
            if self.is_training:
                self.log_message("所有模型训练完成")
                self.update_status("训练完成")
                
                # 发布训练完成事件
                self.publish_event(EventType.MODEL_TRAINING_COMPLETED, {
                    "models": selected_models,
                    "success": True
                })
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            self.log_message(f"训练失败: {str(e)}")
            self.update_status("训练失败")
        
        finally:
            # 重置UI状态
            self.is_training = False
            self.train_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def _reset_params(self):
        """重置参数"""
        self.cv_folds.set(5)
        self.test_size.set(0.2)
        self.random_state.set(42)
        self.scoring_metric.set("accuracy")
        self.enable_feature_selection.set(False)
        self.enable_hyperparameter_tuning.set(False)
        self.enable_early_stopping.set(True)
        
        self.log_message("参数已重置为默认值")
    
    def _save_config(self):
        """保存配置"""
        config = {
            "selected_models": self.get_selected_models(),
            "training_params": self._get_training_params()
        }
        
        # 这里可以实现配置保存逻辑
        self.log_message("配置已保存")
    
    def _load_config(self):
        """加载配置"""
        # 这里可以实现配置加载逻辑
        self.log_message("配置已加载")
    
    def _get_training_params(self) -> Dict[str, Any]:
        """获取训练参数"""
        return {
            "cv_folds": self.cv_folds.get(),
            "test_size": self.test_size.get(),
            "random_state": self.random_state.get(),
            "scoring_metric": self.scoring_metric.get(),
            "enable_feature_selection": self.enable_feature_selection.get(),
            "enable_hyperparameter_tuning": self.enable_hyperparameter_tuning.get(),
            "enable_early_stopping": self.enable_early_stopping.get()
        }
    
    def get_selected_models(self) -> List[str]:
        """获取选中的模型列表"""
        return [model for model, var in self.model_vars.items() if var.get()]
    
    def log_message(self, message: str):
        """记录日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def set_selected_models(self, models: List[str]):
        """设置选中的模型"""
        # 先清空所有选择
        self._deselect_all_models()
        
        # 选择指定模型
        for model in models:
            if model in self.model_vars:
                self.model_vars[model].set(True)
