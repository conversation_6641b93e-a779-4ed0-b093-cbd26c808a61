"""
统一SHAP分析器
提供统一的SHAP分析接口，减少重复代码
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import logging

logger = logging.getLogger(__name__)

# 检查SHAP可用性
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    logger.warning("SHAP库不可用，SHAP分析功能将被禁用")


class UnifiedSHAPAnalyzer:
    """统一的SHAP分析器"""
    
    def __init__(self):
        """初始化SHAP分析器"""
        self.explainers: Dict[str, Any] = {}
        self.shap_values: Dict[str, Any] = {}
        self.feature_names: Optional[List[str]] = None
        self.class_names: Optional[List[str]] = None
        
    def is_available(self) -> bool:
        """检查SHAP是否可用"""
        return SHAP_AVAILABLE
    
    def create_explainer(self, model: Any, X_data: pd.DataFrame, 
                        model_type: str = 'auto', model_name: str = 'default') -> bool:
        """
        创建SHAP解释器
        
        Args:
            model: 训练好的模型
            X_data: 训练数据
            model_type: 模型类型 ('auto', 'tree', 'linear', 'kernel')
            model_name: 模型名称
            
        Returns:
            bool: 是否创建成功
        """
        if not SHAP_AVAILABLE:
            logger.warning("SHAP不可用，跳过解释器创建")
            return False
        
        try:
            # 自动检测模型类型
            if model_type == 'auto':
                model_type = self._detect_model_type(model)
            
            # 创建对应的解释器
            if model_type == 'tree':
                explainer = shap.TreeExplainer(model)
            elif model_type == 'linear':
                explainer = shap.LinearExplainer(model, X_data)
            elif model_type == 'kernel':
                # 对于复杂模型使用KernelExplainer
                if hasattr(model, 'predict_proba'):
                    explainer = shap.KernelExplainer(model.predict_proba, X_data.sample(min(100, len(X_data))))
                else:
                    explainer = shap.KernelExplainer(model.predict, X_data.sample(min(100, len(X_data))))
            else:
                logger.error(f"不支持的模型类型: {model_type}")
                return False
            
            self.explainers[model_name] = explainer
            self.feature_names = list(X_data.columns)
            
            logger.info(f"为模型 {model_name} 创建了 {model_type} 类型的SHAP解释器")
            return True
            
        except Exception as e:
            logger.error(f"创建SHAP解释器失败: {e}")
            return False
    
    def _detect_model_type(self, model: Any) -> str:
        """
        自动检测模型类型
        
        Args:
            model: 模型对象
            
        Returns:
            str: 模型类型
        """
        model_class = model.__class__.__name__.lower()
        
        # 树模型
        tree_models = ['randomforest', 'extratrees', 'gradientboosting', 
                      'xgboost', 'lightgbm', 'catboost', 'decisiontree']
        if any(tree_name in model_class for tree_name in tree_models):
            return 'tree'
        
        # 线性模型
        linear_models = ['logisticregression', 'linearregression', 'ridge', 
                        'lasso', 'elasticnet', 'sgd']
        if any(linear_name in model_class for linear_name in linear_models):
            return 'linear'
        
        # 默认使用kernel解释器
        return 'kernel'
    
    def calculate_shap_values(self, X_test: pd.DataFrame, 
                            model_name: str = 'default',
                            max_samples: int = 100) -> bool:
        """
        计算SHAP值
        
        Args:
            X_test: 测试数据
            model_name: 模型名称
            max_samples: 最大样本数（避免计算时间过长）
            
        Returns:
            bool: 是否计算成功
        """
        if not SHAP_AVAILABLE or model_name not in self.explainers:
            return False
        
        try:
            # 限制样本数量以提高计算速度
            if len(X_test) > max_samples:
                X_sample = X_test.sample(n=max_samples, random_state=42)
                logger.info(f"为提高计算速度，随机采样 {max_samples} 个样本进行SHAP分析")
            else:
                X_sample = X_test
            
            # 计算SHAP值
            explainer = self.explainers[model_name]
            shap_values = explainer.shap_values(X_sample)
            
            self.shap_values[model_name] = {
                'values': shap_values,
                'data': X_sample,
                'feature_names': list(X_sample.columns)
            }
            
            logger.info(f"SHAP值计算完成，模型: {model_name}，样本数: {len(X_sample)}")
            return True
            
        except Exception as e:
            logger.error(f"计算SHAP值失败: {e}")
            return False
    
    def create_summary_plot(self, model_name: str = 'default', 
                          output_dir: Optional[str] = None,
                          plot_type: str = 'dot') -> Optional[str]:
        """
        创建SHAP摘要图
        
        Args:
            model_name: 模型名称
            output_dir: 输出目录
            plot_type: 图表类型 ('dot', 'bar', 'violin')
            
        Returns:
            Optional[str]: 保存的文件路径
        """
        if not SHAP_AVAILABLE or model_name not in self.shap_values:
            return None
        
        try:
            shap_data = self.shap_values[model_name]
            shap_values = shap_data['values']
            X_data = shap_data['data']
            
            plt.figure(figsize=(10, 8))
            
            # 处理多分类情况
            if isinstance(shap_values, list):
                # 多分类：使用第一个类别或平均值
                if plot_type == 'bar':
                    # 对于条形图，计算平均绝对SHAP值
                    mean_shap = np.mean([np.abs(sv).mean(0) for sv in shap_values], axis=0)
                    shap.summary_plot(mean_shap, X_data, plot_type='bar', show=False)
                else:
                    shap.summary_plot(shap_values[0], X_data, plot_type=plot_type, show=False)
            else:
                shap.summary_plot(shap_values, X_data, plot_type=plot_type, show=False)
            
            plt.title(f'SHAP Summary Plot - {model_name}')
            plt.tight_layout()
            
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                file_path = output_path / f'shap_summary_{model_name}_{plot_type}.png'
                plt.savefig(file_path, dpi=300, bbox_inches='tight')
                plt.close()
                return str(file_path)
            else:
                plt.show()
                return None
                
        except Exception as e:
            logger.error(f"创建SHAP摘要图失败: {e}")
            return None
    
    def create_dependence_plot(self, feature_name: str, model_name: str = 'default',
                             output_dir: Optional[str] = None) -> Optional[str]:
        """
        创建SHAP依赖图
        
        Args:
            feature_name: 特征名称
            model_name: 模型名称
            output_dir: 输出目录
            
        Returns:
            Optional[str]: 保存的文件路径
        """
        if not SHAP_AVAILABLE or model_name not in self.shap_values:
            return None
        
        try:
            shap_data = self.shap_values[model_name]
            shap_values = shap_data['values']
            X_data = shap_data['data']
            
            plt.figure(figsize=(10, 6))
            
            # 处理多分类情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]  # 使用第一个类别
            
            shap.dependence_plot(feature_name, shap_values, X_data, show=False)
            plt.title(f'SHAP Dependence Plot - {feature_name} ({model_name})')
            plt.tight_layout()
            
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                safe_feature_name = feature_name.replace('/', '_').replace('\\', '_')
                file_path = output_path / f'shap_dependence_{model_name}_{safe_feature_name}.png'
                plt.savefig(file_path, dpi=300, bbox_inches='tight')
                plt.close()
                return str(file_path)
            else:
                plt.show()
                return None
                
        except Exception as e:
            logger.error(f"创建SHAP依赖图失败: {e}")
            return None
    
    def create_waterfall_plot(self, sample_idx: int, model_name: str = 'default',
                            output_dir: Optional[str] = None) -> Optional[str]:
        """
        创建SHAP瀑布图
        
        Args:
            sample_idx: 样本索引
            model_name: 模型名称
            output_dir: 输出目录
            
        Returns:
            Optional[str]: 保存的文件路径
        """
        if not SHAP_AVAILABLE or model_name not in self.shap_values:
            return None
        
        try:
            shap_data = self.shap_values[model_name]
            shap_values = shap_data['values']
            X_data = shap_data['data']
            
            if sample_idx >= len(X_data):
                logger.error(f"样本索引 {sample_idx} 超出范围")
                return None
            
            plt.figure(figsize=(10, 8))
            
            # 处理多分类情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]  # 使用第一个类别
            
            # 创建Explanation对象
            explanation = shap.Explanation(
                values=shap_values[sample_idx],
                base_values=self.explainers[model_name].expected_value if hasattr(self.explainers[model_name], 'expected_value') else 0,
                data=X_data.iloc[sample_idx].values,
                feature_names=list(X_data.columns)
            )
            
            shap.waterfall_plot(explanation, show=False)
            plt.title(f'SHAP Waterfall Plot - Sample {sample_idx} ({model_name})')
            plt.tight_layout()
            
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                file_path = output_path / f'shap_waterfall_{model_name}_sample_{sample_idx}.png'
                plt.savefig(file_path, dpi=300, bbox_inches='tight')
                plt.close()
                return str(file_path)
            else:
                plt.show()
                return None
                
        except Exception as e:
            logger.error(f"创建SHAP瀑布图失败: {e}")
            return None
    
    def create_force_plot(self, sample_idx: int, model_name: str = 'default',
                         output_dir: Optional[str] = None) -> Optional[str]:
        """
        创建SHAP力图
        
        Args:
            sample_idx: 样本索引
            model_name: 模型名称
            output_dir: 输出目录
            
        Returns:
            Optional[str]: 保存的文件路径
        """
        if not SHAP_AVAILABLE or model_name not in self.shap_values:
            return None
        
        try:
            shap_data = self.shap_values[model_name]
            shap_values = shap_data['values']
            X_data = shap_data['data']
            
            if sample_idx >= len(X_data):
                logger.error(f"样本索引 {sample_idx} 超出范围")
                return None
            
            # 处理多分类情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]  # 使用第一个类别
            
            # 创建力图
            explainer = self.explainers[model_name]
            expected_value = explainer.expected_value if hasattr(explainer, 'expected_value') else 0
            
            force_plot = shap.force_plot(
                expected_value,
                shap_values[sample_idx],
                X_data.iloc[sample_idx],
                matplotlib=True,
                show=False
            )
            
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                file_path = output_path / f'shap_force_{model_name}_sample_{sample_idx}.png'
                plt.savefig(file_path, dpi=300, bbox_inches='tight')
                plt.close()
                return str(file_path)
            else:
                plt.show()
                return None
                
        except Exception as e:
            logger.error(f"创建SHAP力图失败: {e}")
            return None
    
    def create_complete_analysis(self, model_name: str = 'default',
                               output_dir: Optional[str] = None,
                               max_features: int = 10) -> Dict[str, Any]:
        """
        创建完整的SHAP分析
        
        Args:
            model_name: 模型名称
            output_dir: 输出目录
            max_features: 最大特征数
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not SHAP_AVAILABLE or model_name not in self.shap_values:
            return {}
        
        results = {}
        
        try:
            # 创建摘要图
            summary_dot = self.create_summary_plot(model_name, output_dir, 'dot')
            summary_bar = self.create_summary_plot(model_name, output_dir, 'bar')
            
            results['summary_plots'] = {
                'dot': summary_dot,
                'bar': summary_bar
            }
            
            # 创建依赖图（前几个重要特征）
            shap_data = self.shap_values[model_name]
            shap_values = shap_data['values']
            X_data = shap_data['data']
            
            # 计算特征重要性
            if isinstance(shap_values, list):
                importance = np.mean([np.abs(sv).mean(0) for sv in shap_values], axis=0)
            else:
                importance = np.abs(shap_values).mean(0)
            
            # 获取最重要的特征
            feature_importance = list(zip(X_data.columns, importance))
            feature_importance.sort(key=lambda x: x[1], reverse=True)
            
            dependence_plots = {}
            for feature_name, _ in feature_importance[:max_features]:
                plot_path = self.create_dependence_plot(feature_name, model_name, output_dir)
                if plot_path:
                    dependence_plots[feature_name] = plot_path
            
            results['dependence_plots'] = dependence_plots
            
            # 创建样本解释图（前几个样本）
            sample_plots = {}
            n_samples = min(3, len(X_data))
            for i in range(n_samples):
                waterfall_path = self.create_waterfall_plot(i, model_name, output_dir)
                if waterfall_path:
                    sample_plots[f'sample_{i}_waterfall'] = waterfall_path
            
            results['sample_plots'] = sample_plots
            results['feature_importance'] = feature_importance
            
            logger.info(f"完整SHAP分析完成，模型: {model_name}")
            return results
            
        except Exception as e:
            logger.error(f"创建完整SHAP分析失败: {e}")
            return results
