"""
增强的错误处理器
提供统一的错误处理、日志记录和恢复机制
"""

import logging
import traceback
import functools
import threading
from typing import Dict, Any, Optional, Callable, List, Type
from datetime import datetime
from pathlib import Path
import json
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    DATA_ERROR = "data_error"
    MODEL_ERROR = "model_error"
    IO_ERROR = "io_error"
    MEMORY_ERROR = "memory_error"
    NETWORK_ERROR = "network_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorInfo:
    """错误信息类"""
    
    def __init__(self, exception: Exception, context: Dict[str, Any] = None,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 category: ErrorCategory = ErrorCategory.UNKNOWN_ERROR):
        """
        初始化错误信息
        
        Args:
            exception: 异常对象
            context: 错误上下文
            severity: 错误严重程度
            category: 错误类别
        """
        self.exception = exception
        self.context = context or {}
        self.severity = severity
        self.category = category
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()
        self.error_id = self._generate_error_id()
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        import hashlib
        error_data = f"{self.timestamp.isoformat()}{type(self.exception).__name__}{str(self.exception)}"
        return hashlib.md5(error_data.encode()).hexdigest()[:8]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_id': self.error_id,
            'timestamp': self.timestamp.isoformat(),
            'exception_type': type(self.exception).__name__,
            'exception_message': str(self.exception),
            'severity': self.severity.value,
            'category': self.category.value,
            'context': self.context,
            'traceback': self.traceback
        }


class ErrorHandler:
    """增强的错误处理器"""
    
    def __init__(self, log_file: Optional[str] = None):
        """
        初始化错误处理器
        
        Args:
            log_file: 错误日志文件路径
        """
        self.errors: List[ErrorInfo] = []
        self.error_counts: Dict[str, int] = {}
        self.recovery_strategies: Dict[Type[Exception], Callable] = {}
        self.lock = threading.Lock()
        
        # 设置错误日志文件
        if log_file:
            self.log_file = Path(log_file)
            self.log_file.parent.mkdir(parents=True, exist_ok=True)
        else:
            self.log_file = None
        
        # 注册默认恢复策略
        self._register_default_recovery_strategies()
    
    def _register_default_recovery_strategies(self):
        """注册默认恢复策略"""
        self.recovery_strategies.update({
            FileNotFoundError: self._handle_file_not_found,
            MemoryError: self._handle_memory_error,
            ValueError: self._handle_value_error,
            ImportError: self._handle_import_error,
            ConnectionError: self._handle_connection_error
        })
    
    def _handle_file_not_found(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理文件未找到错误"""
        return {
            'recovered': False,
            'suggestion': '请检查文件路径是否正确，或选择其他数据文件',
            'action': 'file_selection_required'
        }
    
    def _handle_memory_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理内存错误"""
        # 尝试垃圾回收
        import gc
        collected = gc.collect()
        
        return {
            'recovered': True,
            'suggestion': f'已执行垃圾回收，释放了 {collected} 个对象。建议减少数据集大小或使用采样',
            'action': 'memory_optimized'
        }
    
    def _handle_value_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理值错误"""
        return {
            'recovered': False,
            'suggestion': '请检查输入数据的格式和值是否正确',
            'action': 'data_validation_required'
        }
    
    def _handle_import_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理导入错误"""
        return {
            'recovered': False,
            'suggestion': '缺少必要的依赖包，请安装相关包或使用其他功能',
            'action': 'dependency_check_required'
        }
    
    def _handle_connection_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理连接错误"""
        return {
            'recovered': False,
            'suggestion': '网络连接失败，请检查网络连接或稍后重试',
            'action': 'retry_later'
        }
    
    def _categorize_error(self, exception: Exception) -> ErrorCategory:
        """自动分类错误"""
        error_type = type(exception)
        error_message = str(exception).lower()
        
        if issubclass(error_type, (FileNotFoundError, PermissionError, IOError)):
            return ErrorCategory.IO_ERROR
        elif issubclass(error_type, MemoryError):
            return ErrorCategory.MEMORY_ERROR
        elif issubclass(error_type, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK_ERROR
        elif issubclass(error_type, (ValueError, TypeError)) and any(
            keyword in error_message for keyword in ['data', 'format', 'shape', 'column']
        ):
            return ErrorCategory.DATA_ERROR
        elif 'model' in error_message or 'predict' in error_message or 'fit' in error_message:
            return ErrorCategory.MODEL_ERROR
        elif issubclass(error_type, (ValueError, TypeError, AssertionError)):
            return ErrorCategory.VALIDATION_ERROR
        else:
            return ErrorCategory.UNKNOWN_ERROR
    
    def _determine_severity(self, exception: Exception, context: Dict[str, Any]) -> ErrorSeverity:
        """确定错误严重程度"""
        error_type = type(exception)
        
        # 关键错误
        if issubclass(error_type, (MemoryError, SystemError)):
            return ErrorSeverity.CRITICAL
        
        # 高严重性错误
        if issubclass(error_type, (ImportError, ModuleNotFoundError)):
            return ErrorSeverity.HIGH
        
        # 中等严重性错误
        if issubclass(error_type, (FileNotFoundError, ValueError, TypeError)):
            return ErrorSeverity.MEDIUM
        
        # 低严重性错误
        return ErrorSeverity.LOW
    
    def handle_error(self, exception: Exception, context: Dict[str, Any] = None,
                    function_name: str = None) -> Dict[str, Any]:
        """
        处理错误
        
        Args:
            exception: 异常对象
            context: 错误上下文
            function_name: 函数名称
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        with self.lock:
            # 创建错误信息
            category = self._categorize_error(exception)
            severity = self._determine_severity(exception, context or {})
            
            error_info = ErrorInfo(
                exception=exception,
                context=context or {},
                severity=severity,
                category=category
            )
            
            # 添加函数名称到上下文
            if function_name:
                error_info.context['function_name'] = function_name
            
            # 记录错误
            self.errors.append(error_info)
            
            # 更新错误计数
            error_key = f"{type(exception).__name__}:{category.value}"
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
            
            # 记录日志
            self._log_error(error_info)
            
            # 尝试恢复
            recovery_result = self._attempt_recovery(error_info)
            
            return {
                'error_id': error_info.error_id,
                'severity': severity.value,
                'category': category.value,
                'recovery': recovery_result,
                'timestamp': error_info.timestamp.isoformat()
            }
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_message = (
            f"错误ID: {error_info.error_id} | "
            f"类型: {type(error_info.exception).__name__} | "
            f"消息: {str(error_info.exception)} | "
            f"严重程度: {error_info.severity.value} | "
            f"类别: {error_info.category.value}"
        )
        
        # 根据严重程度选择日志级别
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # 写入错误日志文件
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    json.dump(error_info.to_dict(), f, ensure_ascii=False)
                    f.write('\n')
            except Exception as e:
                logger.error(f"写入错误日志文件失败: {e}")
    
    def _attempt_recovery(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """尝试错误恢复"""
        exception_type = type(error_info.exception)
        
        # 查找恢复策略
        recovery_func = None
        for error_type, func in self.recovery_strategies.items():
            if issubclass(exception_type, error_type):
                recovery_func = func
                break
        
        if recovery_func:
            try:
                return recovery_func(error_info)
            except Exception as e:
                logger.error(f"错误恢复失败: {e}")
                return {
                    'recovered': False,
                    'suggestion': '自动恢复失败，请手动处理',
                    'action': 'manual_intervention_required'
                }
        else:
            return {
                'recovered': False,
                'suggestion': '未找到合适的恢复策略',
                'action': 'unknown_error_handling'
            }
    
    def register_recovery_strategy(self, exception_type: Type[Exception], 
                                 recovery_func: Callable[[ErrorInfo], Dict[str, Any]]):
        """
        注册错误恢复策略
        
        Args:
            exception_type: 异常类型
            recovery_func: 恢复函数
        """
        self.recovery_strategies[exception_type] = recovery_func
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self.lock:
            total_errors = len(self.errors)
            if total_errors == 0:
                return {'total_errors': 0}
            
            # 按严重程度统计
            severity_counts = {}
            for error in self.errors:
                severity = error.severity.value
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 按类别统计
            category_counts = {}
            for error in self.errors:
                category = error.category.value
                category_counts[category] = category_counts.get(category, 0) + 1
            
            # 最近的错误
            recent_errors = [error.to_dict() for error in self.errors[-5:]]
            
            return {
                'total_errors': total_errors,
                'severity_distribution': severity_counts,
                'category_distribution': category_counts,
                'error_type_counts': dict(self.error_counts),
                'recent_errors': recent_errors
            }
    
    def clear_errors(self):
        """清空错误记录"""
        with self.lock:
            self.errors.clear()
            self.error_counts.clear()


# 全局错误处理器实例
global_error_handler = ErrorHandler()


def error_handled(category: ErrorCategory = None, severity: ErrorSeverity = None):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function_name': func.__name__,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                }
                
                result = global_error_handler.handle_error(
                    exception=e,
                    context=context,
                    function_name=func.__name__
                )
                
                # 根据恢复结果决定是否重新抛出异常
                if not result['recovery']['recovered']:
                    raise e
                
                return None  # 或返回默认值
        
        return wrapper
    return decorator
