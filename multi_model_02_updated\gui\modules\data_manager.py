"""
数据管理模块
处理数据加载、预处理、验证等功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from pathlib import Path
from typing import Optional, Dict, Any, Callable
import logging

from ..core.base_gui import BaseGUI
from ..core.event_manager import EventType

logger = logging.getLogger(__name__)


class DataManager(BaseGUI):
    """数据管理器"""
    
    def __init__(self, parent: tk.Widget):
        """
        初始化数据管理器
        
        Args:
            parent: 父组件
        """
        self.current_data: Optional[pd.DataFrame] = None
        self.current_data_path = tk.StringVar()
        self.target_column = tk.StringVar()
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置数据管理UI"""
        self.main_frame = self.factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建数据加载区域
        self._create_data_loading_section()
        
        # 创建数据预览区域
        self._create_data_preview_section()
        
        # 创建数据信息区域
        self._create_data_info_section()
    
    def _create_data_loading_section(self):
        """创建数据加载区域"""
        load_frame = self.factory.create_label_frame(self.main_frame, text="数据加载")
        load_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 文件路径选择
        path_frame = self.factory.create_frame(load_frame)
        path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.factory.create_label(path_frame, text="数据文件:").pack(side=tk.LEFT)
        
        path_entry = self.factory.create_entry(
            path_frame, textvariable=self.current_data_path, width=50
        )
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        browse_btn = self.factory.create_button(
            path_frame, text="浏览", command=self._browse_data_file
        )
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 目标列选择
        target_frame = self.factory.create_frame(load_frame)
        target_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.factory.create_label(target_frame, text="目标列:").pack(side=tk.LEFT)
        
        self.target_combobox = self.factory.create_combobox(
            target_frame, textvariable=self.target_column, width=20
        )
        self.target_combobox.pack(side=tk.LEFT, padx=(5, 0))
        
        # 操作按钮
        button_frame = self.factory.create_frame(load_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        buttons = [
            {"text": "加载数据", "command": self._load_data, "style": "Primary.TButton"},
            {"text": "验证数据", "command": self._validate_data},
            {"text": "预处理", "command": self._preprocess_data},
            {"text": "清空", "command": self._clear_data}
        ]
        
        self.create_button_group(button_frame, buttons)
    
    def _create_data_preview_section(self):
        """创建数据预览区域"""
        preview_frame = self.factory.create_label_frame(self.main_frame, text="数据预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 创建树形视图用于显示数据
        columns = ["索引"] + [f"列{i}" for i in range(10)]  # 预设列数
        self.data_tree = self.factory.create_treeview(
            preview_frame, columns=columns, show="headings", height=10
        )
        
        # 配置列
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=80, minwidth=50)
        
        # 添加滚动条
        tree_scrollbar_v = self.factory.create_scrollbar(
            preview_frame, orient="vertical", command=self.data_tree.yview
        )
        tree_scrollbar_h = self.factory.create_scrollbar(
            preview_frame, orient="horizontal", command=self.data_tree.xview
        )
        
        self.data_tree.configure(
            yscrollcommand=tree_scrollbar_v.set,
            xscrollcommand=tree_scrollbar_h.set
        )
        
        # 布局
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
    
    def _create_data_info_section(self):
        """创建数据信息区域"""
        info_frame = self.factory.create_label_frame(self.main_frame, text="数据信息")
        info_frame.pack(fill=tk.X)
        
        # 创建信息显示区域
        info_text_frame = self.factory.create_frame(info_frame)
        info_text_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.info_text = self.factory.create_text(
            info_text_frame, height=6, wrap=tk.WORD
        )
        
        info_scrollbar = self.factory.create_scrollbar(
            info_text_frame, orient="vertical", command=self.info_text.yview
        )
        
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _browse_data_file(self):
        """浏览数据文件"""
        file_types = [
            ("CSV文件", "*.csv"),
            ("Excel文件", "*.xlsx *.xls"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=file_types
        )
        
        if filename:
            self.current_data_path.set(filename)
    
    def _load_data(self):
        """加载数据"""
        data_path = self.current_data_path.get().strip()
        if not data_path:
            self.show_warning("警告", "请先选择数据文件")
            return
        
        if not Path(data_path).exists():
            self.show_error("错误", "数据文件不存在")
            return
        
        try:
            self.update_status("正在加载数据...")
            
            # 根据文件扩展名选择加载方法
            file_ext = Path(data_path).suffix.lower()
            
            if file_ext == '.csv':
                self.current_data = pd.read_csv(data_path, encoding='utf-8')
            elif file_ext in ['.xlsx', '.xls']:
                self.current_data = pd.read_excel(data_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            # 更新目标列选项
            self._update_target_columns()
            
            # 更新数据预览
            self._update_data_preview()
            
            # 更新数据信息
            self._update_data_info()
            
            self.update_status(f"数据加载成功: {self.current_data.shape[0]} 行, {self.current_data.shape[1]} 列")
            
            # 发布数据加载事件
            self.publish_event(EventType.DATA_LOADED, {
                "data": self.current_data,
                "path": data_path,
                "shape": self.current_data.shape
            })
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            self.show_error("错误", f"数据加载失败: {str(e)}")
            self.update_status("数据加载失败")
    
    def _update_target_columns(self):
        """更新目标列选项"""
        if self.current_data is not None:
            columns = list(self.current_data.columns)
            self.target_combobox['values'] = columns
            
            # 自动选择可能的目标列
            target_candidates = ['target', 'label', 'y', 'class', 'outcome']
            for candidate in target_candidates:
                if candidate in columns:
                    self.target_column.set(candidate)
                    break
            else:
                # 如果没有找到候选列，选择最后一列
                if columns:
                    self.target_column.set(columns[-1])
    
    def _update_data_preview(self):
        """更新数据预览"""
        if self.current_data is None:
            return
        
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # 更新列标题
        columns = ["索引"] + list(self.current_data.columns)
        self.data_tree['columns'] = columns
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100, minwidth=50)
        
        # 显示前100行数据
        preview_rows = min(100, len(self.current_data))
        for i in range(preview_rows):
            row_data = [str(i)] + [str(val) for val in self.current_data.iloc[i]]
            self.data_tree.insert("", "end", values=row_data)
    
    def _update_data_info(self):
        """更新数据信息"""
        if self.current_data is None:
            return
        
        # 清空现有信息
        self.info_text.delete(1.0, tk.END)
        
        # 生成数据信息
        info_lines = [
            f"数据形状: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列",
            f"内存使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB",
            "",
            "列信息:",
        ]
        
        for col in self.current_data.columns:
            dtype = str(self.current_data[col].dtype)
            null_count = self.current_data[col].isnull().sum()
            unique_count = self.current_data[col].nunique()
            
            info_lines.append(
                f"  {col}: {dtype}, 缺失值: {null_count}, 唯一值: {unique_count}"
            )
        
        # 显示基本统计信息
        if len(self.current_data.select_dtypes(include=['number']).columns) > 0:
            info_lines.extend(["", "数值列统计:"])
            numeric_cols = self.current_data.select_dtypes(include=['number']).columns
            for col in numeric_cols[:5]:  # 只显示前5个数值列
                series = self.current_data[col]
                info_lines.append(
                    f"  {col}: 均值={series.mean():.2f}, 标准差={series.std():.2f}, "
                    f"最小值={series.min():.2f}, 最大值={series.max():.2f}"
                )
        
        # 插入信息到文本框
        self.info_text.insert(tk.END, "\n".join(info_lines))
    
    def _validate_data(self):
        """验证数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        target_col = self.target_column.get()
        if not target_col:
            self.show_warning("警告", "请选择目标列")
            return
        
        if target_col not in self.current_data.columns:
            self.show_error("错误", f"目标列 '{target_col}' 不存在")
            return
        
        # 执行数据验证
        validation_results = []
        
        # 检查缺失值
        missing_counts = self.current_data.isnull().sum()
        if missing_counts.sum() > 0:
            validation_results.append(f"发现缺失值: {missing_counts.sum()} 个")
        
        # 检查重复行
        duplicate_count = self.current_data.duplicated().sum()
        if duplicate_count > 0:
            validation_results.append(f"发现重复行: {duplicate_count} 行")
        
        # 检查目标列
        target_unique = self.current_data[target_col].nunique()
        validation_results.append(f"目标列唯一值数量: {target_unique}")
        
        if validation_results:
            result_text = "\n".join(validation_results)
            self.show_info("数据验证结果", result_text)
        else:
            self.show_info("数据验证结果", "数据验证通过，未发现问题")
        
        # 发布数据验证事件
        self.publish_event(EventType.DATA_VALIDATED, {
            "results": validation_results,
            "target_column": target_col
        })
    
    def _preprocess_data(self):
        """预处理数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            self.update_status("正在预处理数据...")
            
            # 这里可以添加具体的预处理逻辑
            # 例如：处理缺失值、编码分类变量等
            
            self.update_status("数据预处理完成")
            self.show_info("成功", "数据预处理完成")
            
            # 发布数据预处理事件
            self.publish_event(EventType.DATA_PREPROCESSED, {
                "data": self.current_data
            })
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            self.show_error("错误", f"数据预处理失败: {str(e)}")
            self.update_status("数据预处理失败")
    
    def _clear_data(self):
        """清空数据"""
        if self.ask_yes_no("确认", "确定要清空当前数据吗？"):
            self.current_data = None
            self.current_data_path.set("")
            self.target_column.set("")
            
            # 清空预览
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
            # 清空信息
            self.info_text.delete(1.0, tk.END)
            
            # 清空目标列选项
            self.target_combobox['values'] = []
            
            self.update_status("数据已清空")
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self.current_data
    
    def get_target_column(self) -> str:
        """获取目标列名"""
        return self.target_column.get()
    
    def set_data(self, data: pd.DataFrame, target_col: str = None):
        """
        设置数据
        
        Args:
            data: 数据框
            target_col: 目标列名
        """
        self.current_data = data
        self._update_target_columns()
        self._update_data_preview()
        self._update_data_info()
        
        if target_col and target_col in data.columns:
            self.target_column.set(target_col)
